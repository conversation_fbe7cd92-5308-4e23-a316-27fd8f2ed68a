{"cells": [{"cell_type": "markdown", "metadata": {"id": "yeUrRA8V3b75"}, "source": ["# UX Audit Report Evaluation Using Gemini AI\n", "\n", "This notebook evaluates model-generated UX audit reports against ground truth data using Google's Gemini AI model.\n", "\n", "## Evaluation Parameters:\n", "1. **Observation Coverage** - How many GT observations were matched by the model\n", "2. **Heuristic Match Accuracy** - Precision, recall, and F1-score for heuristics\n", "3. **Severity Consistency** - Comparison of severity ratings\n", "4. **Location Accuracy** - Semantic similarity of issue locations\n", "5. **Observation Similarity** - Semantic comparison of observation texts\n", "6. **False Positives/Hallucinations** - Issues invented by the model\n", "7. **False Negatives/Omissions** - GT issues missed by the model\n", "8. **Overall Evaluation Summary** - Comprehensive analysis by Gemini"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "u_hAzWRy3b7-", "executionInfo": {"status": "ok", "timestamp": 1749815073154, "user_tz": -330, "elapsed": 22457, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "e253ec2c-4eda-4495-9228-3b89820d390d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: google-generativeai in /usr/local/lib/python3.11/dist-packages (0.8.5)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (2.2.2)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (2.0.2)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.11/dist-packages (3.10.0)\n", "Requirement already satisfied: seaborn in /usr/local/lib/python3.11/dist-packages (0.13.2)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (1.6.1)\n", "Requirement already satisfied: google-ai-generativelanguage==0.6.15 in /usr/local/lib/python3.11/dist-packages (from google-generativeai) (0.6.15)\n", "Requirement already satisfied: google-api-core in /usr/local/lib/python3.11/dist-packages (from google-generativeai) (2.25.0)\n", "Requirement already satisfied: google-api-python-client in /usr/local/lib/python3.11/dist-packages (from google-generativeai) (2.171.0)\n", "Requirement already satisfied: google-auth>=2.15.0 in /usr/local/lib/python3.11/dist-packages (from google-generativeai) (2.38.0)\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.11/dist-packages (from google-generativeai) (5.29.5)\n", "Requirement already satisfied: pydantic in /usr/local/lib/python3.11/dist-packages (from google-generativeai) (2.11.5)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.11/dist-packages (from google-generativeai) (4.67.1)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.11/dist-packages (from google-generativeai) (4.14.0)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in /usr/local/lib/python3.11/dist-packages (from google-ai-generativelanguage==0.6.15->google-generativeai) (1.26.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas) (2025.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (4.58.1)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (1.4.8)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (24.2)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (11.2.1)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (3.2.3)\n", "Requirement already satisfied: scipy>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.15.3)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (3.6.0)\n", "Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in /usr/local/lib/python3.11/dist-packages (from google-api-core->google-generativeai) (1.70.0)\n", "Requirement already satisfied: requests<3.0.0,>=2.18.0 in /usr/local/lib/python3.11/dist-packages (from google-api-core->google-generativeai) (2.32.3)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from google-auth>=2.15.0->google-generativeai) (5.5.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.11/dist-packages (from google-auth>=2.15.0->google-generativeai) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.11/dist-packages (from google-auth>=2.15.0->google-generativeai) (4.9.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n", "Requirement already satisfied: httplib2<1.0.0,>=0.19.0 in /usr/local/lib/python3.11/dist-packages (from google-api-python-client->google-generativeai) (0.22.0)\n", "Requirement already satisfied: google-auth-httplib2<1.0.0,>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from google-api-python-client->google-generativeai) (0.2.0)\n", "Requirement already satisfied: uritemplate<5,>=3.0.1 in /usr/local/lib/python3.11/dist-packages (from google-api-python-client->google-generativeai) (4.2.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic->google-generativeai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic->google-generativeai) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic->google-generativeai) (0.4.1)\n", "Requirement already satisfied: grpcio<2.0.0,>=1.33.2 in /usr/local/lib/python3.11/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.15->google-generativeai) (1.72.1)\n", "Requirement already satisfied: grpcio-status<2.0.0,>=1.33.2 in /usr/local/lib/python3.11/dist-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.15->google-generativeai) (1.71.0)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.6.1 in /usr/local/lib/python3.11/dist-packages (from pyasn1-modules>=0.2.1->google-auth>=2.15.0->google-generativeai) (0.6.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3.0.0,>=2.18.0->google-api-core->google-generativeai) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3.0.0,>=2.18.0->google-api-core->google-generativeai) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests<3.0.0,>=2.18.0->google-api-core->google-generativeai) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests<3.0.0,>=2.18.0->google-api-core->google-generativeai) (2025.4.26)\n"]}], "source": ["# Install required packages\n", "!pip install google-generativeai pandas numpy mat<PERSON><PERSON><PERSON>b seaborn scikit-learn"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "D4tsdmyF3b7_", "executionInfo": {"status": "ok", "timestamp": 1749815080096, "user_tz": -330, "elapsed": 6936, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}}, "outputs": [], "source": ["# Import required libraries\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import Dict, List, Tuple, Any\n", "import google.generativeai as genai\n", "from sklearn.metrics import precision_recall_fscore_support\n", "import re\n", "from collections import defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 8)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aFyoBB933b7_", "executionInfo": {"status": "ok", "timestamp": 1749815080101, "user_tz": -330, "elapsed": 12, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "ace0bcd4-29b9-40b9-eccc-745ab51fac37"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["✅ Gemini AI configured successfully!\n"]}], "source": ["# Configure Gemini API\n", "# Replace 'YOUR_API_KEY' with your actual Gemini API key\n", "GEMINI_API_KEY = 'AIzaSyCJaADIHnUd3TmZDfyeh2JKk_k8WO6t7JI'  # Get from https://makersuite.google.com/app/apikey\n", "genai.configure(api_key=GEMINI_API_KEY)\n", "\n", "# Initialize Gemini model\n", "model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')\n", "\n", "print(\"✅ Gemini AI configured successfully!\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nca5wNOv3b8A", "executionInfo": {"status": "ok", "timestamp": 1749815154954, "user_tz": -330, "elapsed": 14, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "d539be1f-bebe-4c73-8fa1-6b8abb594e1e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["📊 Loaded 10 ground truth observations\n", "📊 Loaded 12 model response observations\n", "\n", "✅ Data loaded successfully!\n"]}], "source": ["# Load data files\n", "def load_json_data(file_path: str) -> Dict:\n", "    \"\"\"Load JSON data from file\"\"\"\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            return json.load(f)\n", "    except Exception as e:\n", "        print(f\"Error loading {file_path}: {e}\")\n", "        return {}\n", "\n", "# Load ground truth and model response data\n", "ground_truth = load_json_data('ground_truth.json')\n", "model_response = load_json_data('latest.json')\n", "\n", "# Extract observations\n", "gt_observations = ground_truth.get('audit_report', {}).get('observations', [])\n", "model_observations = model_response.get('audit_report', {}).get('observations', [])\n", "\n", "print(f\"📊 Loaded {len(gt_observations)} ground truth observations\")\n", "print(f\"📊 Loaded {len(model_observations)} model response observations\")\n", "print(\"\\n✅ Data loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "c46Aw1n13b8A"}, "source": ["## Step 1: Location and Observation Matching Using Gemini\n", "\n", "We use Gemini to semantically match observations between ground truth and model response based on location similarity and observation content overlap."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jDXNrMZS3b8A", "executionInfo": {"status": "ok", "timestamp": 1749815158634, "user_tz": -330, "elapsed": 12, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "b54a6e66-02f6-4b09-c4d9-e25999d6cb72"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🔧 Gemini similarity function ready!\n"]}], "source": ["def get_gemini_similarity_score(text1: str, text2: str, context: str = \"\") -> Dict:\n", "    \"\"\"Get semantic similarity score and analysis from Gemini\"\"\"\n", "    prompt = f\"\"\"\n", "    Compare these two UX audit observations and provide:\n", "    1. Similarity score (0-100)\n", "    2. Brief analysis of similarities and differences\n", "    3. Whether they refer to the same UX issue (yes/no)\n", "\n", "    Context: {context}\n", "\n", "    Text 1: {text1}\n", "    Text 2: {text2}\n", "\n", "    Respond in JSON format:\n", "    {{\n", "        \"similarity_score\": <0-100>,\n", "        \"same_issue\": <true/false>,\n", "        \"analysis\": \"<brief analysis>\"\n", "    }}\n", "    \"\"\"\n", "\n", "    try:\n", "        response = model.generate_content(prompt)\n", "        # Extract JSON from response\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            return json.loads(json_match.group())\n", "        else:\n", "            return {\"similarity_score\": 0, \"same_issue\": False, \"analysis\": \"Failed to parse response\"}\n", "    except Exception as e:\n", "        print(f\"Error in Gemini API call: {e}\")\n", "        return {\"similarity_score\": 0, \"same_issue\": False, \"analysis\": f\"API Error: {e}\"}\n", "\n", "print(\"🔧 Gemini similarity function ready!\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 225}, "id": "ZGcorVoq3b8A", "executionInfo": {"status": "ok", "timestamp": 1749815199490, "user_tz": -330, "elapsed": 39697, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "8f6a2c2f-5bc0-4b5b-9b64-c9288e25f80b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🔍 Finding optimal observation matches using Gemini AI...\n", "📊 Processing 10 GT observations vs 12 model observations\n", "\n", "🚀 Step 1: <PERSON><PERSON> analyzing all GT-Model pairs...\n", "🤖 Sending batch request to Gemini...\n", "✅ Batch analysis complete! Found 10 potential matches\n", "\n", "🎯 Step 3: Resolving conflicts for optimal 1:1 matching...\n", "\n", "🎯 Final Results: 9 optimal 1:1 matches found\n", "📊 GT Coverage: 9/10 (90.0%)\n", "📊 Model Coverage: 9/12 (75.0%)\n"]}], "source": ["def find_observation_matches_optimized(gt_obs: List[Dict], model_obs: List[Dict]) -> List[Dict]:\n", "    \"\"\"Find optimal 1:1 matches between GT and model observations using Gemini efficiently\"\"\"\n", "    print(\"🔍 Finding optimal observation matches using Gemini AI...\")\n", "    print(f\"📊 Processing {len(gt_obs)} GT observations vs {len(model_obs)} model observations\")\n", "\n", "    # Step 1: Batch similarity analysis for all pairs\n", "    print(\"\\n🚀 Step 1: <PERSON><PERSON> analyzing all GT-Model pairs...\")\n", "\n", "    similarity_matrix = []\n", "\n", "    # Create batch prompt for all comparisons to reduce API calls\n", "    batch_prompt = f\"\"\"\n", "    Compare these UX audit observations and provide similarity scores for each pair.\n", "    For each comparison, consider both location similarity and observation content similarity.\n", "\n", "    GROUND TRUTH OBSERVATIONS:\n", "    {json.dumps([{\"id\": i, \"location\": gt[\"location\"], \"observation\": gt[\"observation\"][:200] + \"...\"} for i, gt in enumerate(gt_obs)], indent=2)}\n", "\n", "    MODEL RESPONSE OBSERVATIONS:\n", "    {json.dumps([{\"id\": i, \"location\": model[\"location\"], \"observation\": model[\"observation\"][:200] + \"...\"} for i, model in enumerate(model_obs)], indent=2)}\n", "\n", "    For each GT observation, find the best matching model observation and provide:\n", "\n", "    Respond in JSON format:\n", "    {{\n", "        \"matches\": [\n", "            {{\n", "                \"gt_id\": <gt_index>,\n", "                \"best_model_id\": <model_index or -1 if no good match>,\n", "                \"location_similarity\": <0-100>,\n", "                \"observation_similarity\": <0-100>,\n", "                \"combined_score\": <0-100>,\n", "                \"is_valid_match\": <true/false>,\n", "                \"reasoning\": \"brief explanation\"\n", "            }}\n", "        ]\n", "    }}\n", "\n", "    Only consider matches with combined_score > 40. Set best_model_id to -1 if no good match exists.\n", "    \"\"\"\n", "\n", "    try:\n", "        print(\"🤖 Sending batch request to Gemini...\")\n", "        response = model.generate_content(batch_prompt)\n", "\n", "        # Extract JSON from response\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            batch_results = json.loads(json_match.group())\n", "            potential_matches = batch_results.get('matches', [])\n", "            print(f\"✅ Batch analysis complete! Found {len(potential_matches)} potential matches\")\n", "        else:\n", "            print(\"❌ Failed to parse batch response, falling back to individual analysis\")\n", "            potential_matches = []\n", "    except Exception as e:\n", "        print(f\"❌ Batch analysis failed: {e}\")\n", "        print(\"🔄 Falling back to optimized individual analysis...\")\n", "        potential_matches = []\n", "\n", "    # Step 2: If batch failed, use optimized individual analysis\n", "    if not potential_matches:\n", "        potential_matches = []\n", "        for i, gt_item in enumerate(gt_obs):\n", "            print(f\"\\n📍 Processing GT {i+1}/{len(gt_obs)}: {gt_item['location'][:30]}...\")\n", "\n", "            # Quick location-based filtering first\n", "            candidate_models = []\n", "            for j, model_item in enumerate(model_obs):\n", "                # Simple keyword overlap check for initial filtering\n", "                gt_keywords = set(gt_item['location'].lower().split())\n", "                model_keywords = set(model_item['location'].lower().split())\n", "                keyword_overlap = len(gt_keywords.intersection(model_keywords)) / max(len(gt_keywords), 1)\n", "\n", "                if keyword_overlap > 0.2:  # At least 20% keyword overlap\n", "                    candidate_models.append((j, model_item, keyword_overlap))\n", "\n", "            # Sort by keyword overlap and take top 3 candidates\n", "            candidate_models.sort(key=lambda x: x[2], reverse=True)\n", "            top_candidates = candidate_models[:3]\n", "\n", "            if not top_candidates:\n", "                print(f\"  ❌ No location candidates found\")\n", "                potential_matches.append({\n", "                    'gt_id': i,\n", "                    'best_model_id': -1,\n", "                    'combined_score': 0,\n", "                    'is_valid_match': <PERSON><PERSON><PERSON>,\n", "                    'reasoning': 'No location similarity found'\n", "                })\n", "                continue\n", "\n", "            # Use Gemini only for top candidates\n", "            best_match = None\n", "            best_score = 0\n", "\n", "            candidates_text = \"\\n\".join([f\"Model {idx}: {item['location']} - {item['observation'][:100]}...\"\n", "                                        for idx, item, _ in top_candidates])\n", "\n", "            candidate_prompt = f\"\"\"\n", "            Find the best match for this GT observation among the candidates:\n", "\n", "            GT Observation:\n", "            Location: {gt_item['location']}\n", "            Description: {gt_item['observation'][:200]}...\n", "\n", "            Candidates:\n", "            {candidates_text}\n", "\n", "            Respond in JSON format:\n", "            {{\n", "                \"best_model_id\": <model_index or -1>,\n", "                \"location_similarity\": <0-100>,\n", "                \"observation_similarity\": <0-100>,\n", "                \"combined_score\": <0-100>,\n", "                \"is_valid_match\": <true/false>,\n", "                \"reasoning\": \"brief explanation\"\n", "            }}\n", "            \"\"\"\n", "\n", "            try:\n", "                response = model.generate_content(candidate_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    result = json.loads(json_match.group())\n", "                    result['gt_id'] = i\n", "                    potential_matches.append(result)\n", "                    if result['is_valid_match']:\n", "                        print(f\"  ✅ Match found: Model {result['best_model_id']} (Score: {result['combined_score']})\")\n", "                    else:\n", "                        print(f\"  ❌ No valid match (Best score: {result['combined_score']})\")\n", "                else:\n", "                    print(f\"  ❌ Failed to parse response\")\n", "            except Exception as e:\n", "                print(f\"  ❌ Error: {e}\")\n", "\n", "    # Step 3: Resolve conflicts and create optimal 1:1 matching\n", "    print(\"\\n🎯 Step 3: Resolving conflicts for optimal 1:1 matching...\")\n", "\n", "    # Filter valid matches and sort by score\n", "    valid_matches = [m for m in potential_matches if m['is_valid_match'] and m['best_model_id'] != -1]\n", "    valid_matches.sort(key=lambda x: x['combined_score'], reverse=True)\n", "\n", "    # Resolve conflicts using Hungarian-like approach (greedy for simplicity)\n", "    used_model_ids = set()\n", "    used_gt_ids = set()\n", "    final_matches = []\n", "\n", "    for match in valid_matches:\n", "        gt_id = match['gt_id']\n", "        model_id = match['best_model_id']\n", "\n", "        if gt_id not in used_gt_ids and model_id not in used_model_ids:\n", "            # Create final match object\n", "            final_match = {\n", "                'gt_index': gt_id,\n", "                'model_index': model_id,\n", "                'gt_item': gt_obs[gt_id],\n", "                'model_item': model_obs[model_id],\n", "                'location_similarity': {'similarity_score': match['location_similarity']},\n", "                'observation_similarity': {'similarity_score': match['observation_similarity']},\n", "                'combined_score': match['combined_score'],\n", "                'reasoning': match['reasoning']\n", "            }\n", "            final_matches.append(final_match)\n", "            used_gt_ids.add(gt_id)\n", "            used_model_ids.add(model_id)\n", "\n", "    print(f\"\\n🎯 Final Results: {len(final_matches)} optimal 1:1 matches found\")\n", "    print(f\"📊 GT Coverage: {len(final_matches)}/{len(gt_obs)} ({len(final_matches)/len(gt_obs)*100:.1f}%)\")\n", "    print(f\"📊 Model Coverage: {len(final_matches)}/{len(model_obs)} ({len(final_matches)/len(model_obs)*100:.1f}%)\")\n", "\n", "    return final_matches\n", "\n", "# Find optimal matches between observations\n", "observation_matches = find_observation_matches_optimized(gt_observations, model_observations)"]}, {"cell_type": "markdown", "metadata": {"id": "NdpKpiIX3b8B"}, "source": ["## Step 2: Observation Coverage Analysis\n", "\n", "Calculate how many ground truth observations were successfully matched by the model."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 592}, "id": "U5vYV7ud3b8B", "executionInfo": {"status": "ok", "timestamp": 1749815199917, "user_tz": -330, "elapsed": 428, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "e33f52c1-804f-457a-8bc9-13f5b277f5b3"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["📊 OBSERVATION COVERAGE ANALYSIS\n", "========================================\n", "Total Ground Truth Observations: 10\n", "Successfully Matched: 9\n", "Coverage Percentage: 90.0%\n", "Unmatched Observations: 1\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1500x600 with 2 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAABXEAAAJNCAYAAAB3DjZtAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAo/ZJREFUeJzs3Xd4FNX+x/HPpvcAgSSU0Htv0qugIFhABPGiYPfaEAULXntBRUWxi9eu6LVfFREREQUpAlKk954EAul9c35/cNlfliTUJGeTfb+eZx/YmTMzn9lsNjPfPXPGYYwxAgAAAAAAAAB4JB/bAQAAAAAAAAAAJaOICwAAAAAAAAAejCIuAAAAAAAAAHgwirgAAAAAAAAA4MEo4gIAAAAAAACAB6OICwAAAAAAAAAejCIuAAAAAAAAAHgwirgAAAAAAAAA4MEo4gIAAAAAAACAB6OIC4/Vr18/ORwOORwOXX311bbjVGg7d+50vZYOh0O//vqr7UgAAAAA/ufXX391O17fuXNnuWz36quvdm2zX79+5bJNG+rXr+/az0ceecR2nArN1nsVAEVclIPVq1frlltuUZs2bVSlShUFBAQoJiZG5557rp577jmlpKTYjlihVcYCbUJCgh5//HH17dtXMTExCggIUGhoqFq1aqXrrrtOs2fPljHGdkwAAFAJcRyCU/XII4+4HYc7HA5Nmzat2LaTJ08u0va9994r38CVwO+//65rr71WzZo1U3h4uAIDA1WrVi0NGTJEb775prKzs21HrNAo0AKezc92AFRe+fn5mjhxol566aUi8xITE5WYmKj58+frmWee0ccff6zzzz/fQkrvUK1aNT377LOu540aNbKY5sRee+01TZw4scgBWF5entavX6/169frnXfe0Y4dO1S/fn07IQEAQKXEcQjO1quvvqoJEybIx+f/+0tlZWXprbfeOuFyjRo1cjter1atWpllLGz06NFq3bq1JCkuLq5ctnkm0tPTdd111+mzzz4rMu/AgQM6cOCAZs+eraefflpffPGFOnXqZCGld7D1XgVAERdl6Pbbb9cbb7zhel6rVi2NGjVK1atX19q1a/XFF1/I6XTq0KFDuuiii/TLL7+oZ8+eFhOXjtTUVEVERNiO4SYiIkKTJk2yHeOkpk6dqnvvvdf13NfXV0OHDlWnTp3kcDi0detWzZkzRwkJCRZTli5PfL8AAOCNOA5Badi+fbu+//57XXzxxa5pH3/8sZKSkk64XFxcnJXj9cGDB2vw4MHlvt3TUVBQoMsvv1w//PCDa1qTJk00fPhwhYeHa/Hixa55O3fu1HnnnaelS5eqSZMmtiKXGk/8HbX1XgUgyQBlYNGiRUaS69GxY0eTkpLi1mbevHnGx8fH1aZVq1bG6XS65vft29c1b9y4cWbr1q1m5MiRplq1aiY4ONj07NnTzJ07t8i2d+7caW688UbTuHFjExQUZAIDA02tWrVMjx49zJ133mnWr19fZJnffvvNXH755SYuLs4EBASY8PBw061bN/PKK6+Y3NzcIu0L79u7775rvvnmG9O9e3cTGhpqIiMjzQMPPOCaX79+/SLLb9iwwW0dCxcuNMYY89dff5mbb77ZdOnSxdSqVcuVv27dumbUqFHm999/d1tPvXr13NZz/KNv377GGGN27NjhNn3+/PlFMn3xxRdmyJAhJiYmxvj7+5sqVaqY7t27m+eee85kZGSc9DX46aefTL9+/UxoaKgJCwszgwcPNn///XeR5Uqybt064+vr61pndHS0WblyZZF2ubm5ZsaMGSYhIcFt+t69e82kSZNM69atTWhoqAkMDDT16tUzY8aMMUuXLnVre+WVVxZ5jQr74YcfXPN9fHzM7t27XfOys7PNyy+/bHr37m2qVq1q/P39TWxsrLnsssvMH3/8UWRd7777rttrlZGRYe6//37ToEED4+fnZ+644w5jjDHz58831157renQoYOJjY01AQEBJjg42DRq1MhcffXVZs2aNcW+bjt37jRXXHGFqVatmgkNDTW9e/c28+bNK7Ld453ufgAAUJlxHFIxj0N69erldr5wvNdee801PyIiwmRmZrpyn+75QnEefvhht/08dm4zYMAAt3Zt2rQxktzeY8eOoY+ZP3++27wdO3a45qWnp5tHH33UdOjQwYSFhRk/Pz9To0YN065dO3P99deb2bNnu23vt99+M8OGDTO1atUy/v7+JjQ01NSrV88MHjzYPPzwwyY5OdnVdty4cSW+H8/meP+tt94yrVu3NoGBgaZOnTpm4sSJJj093e385eGHHz6l1/njjz92y3LBBReYnJwctzbvvfeeW5vBgwe7zT9+uytWrDCDBw82ERERJiwszJx//vlm+fLlRba9Zs0aM2bMGFOvXj0TEBBggoKCTFxcnOnfv7+57777zN69e4ss8+2335qLL77YxMbGus6r+vfvbz766CNTUFDg1ra487R///vfpkOHDiYoKMi0a9fujD8zTvf3+kTnlYV/x070XjXGmPz8fPP222+bc88910RFRRk/Pz9TrVo1069fPzNjxgyTl5d30tfgk08+MV26dDHBwcGmSpUq5rLLLnP7LDzmv//9rxk0aJCJjo42fn5+Jjw83DRs2NBccsklZsqUKW71BaAyoIiLMlH4YECS+fnnn4ttd8UVV7i1+/XXX13zChdxu3fvbqpVq1bkD4mPj4/57LPPXMskJCSYGjVqnPCPz+uvv+6W4f777z9h+969e5v09HS3ZY6fX/h5ZGSk2bp1q9u04w9GH3zwQde8pk2buqa//PLLJ8zicDjcDvZKo4ibn59vRo0adcL1tGjRwuzfv7/E16Bnz57G4XAUWS4qKsokJiaW/EYp5J///Kfbsl9++eUpLWeMMQsWLDBVq1YtMb+Pj495/vnnXe3nzZvnNu/4g6+rrrrKNf/88893TU9MTDTt27c/4XZefPFFt3UdfxJz/Pvl2MnTxIkTT/gzCAgIKPKlxY4dO0xsbGyxOYYOHeo2rbAz2Q8AACozjkMq5nHI22+/7VouIiLCZGVluc0vvL833nijMebMzhdKcnwRd9iwYa7/r1u3zhhjzC+//OKaNnz4cLf2p1rE7dev3wnzXn755a62P//8c5Fi8fGPDRs2uNqfahH3dI7377vvvmK326VLFxMTE+N6fqpF3MLnhT4+PmbTpk3Ftuvevbvb9nbu3OmaV/i8acCAASYwMLBIvuDgYLdOM+vWrTMhISEnfC0LF9CdTqfb729xj5EjR5r8/HzXMsefpx3/O9quXbsz/sw43d/rE7WVTq2Im56ebvr06XPC9fTq1cukpaWV+BoU/nKm8KNJkyZuv+PHf74V9zj+MwGo6BhOAWXi999/d/2/atWqGjBgQLHtLr/8cn3yySduy/Xt27dIu8WLF6tWrVq69957lZaWprfffls5OTkqKCjQjTfeqPPPP1+RkZH68ssvdfDgQdd2r7nmGkVFRWn//v3auHGjWy5J+vTTTzVlyhTX80GDBqlnz55KSEjQ+++/r/T0dP3++++68847NWPGjBL3tXr16ho9erSioqK0bt06NWrUSH369NFvv/0mSZo5c6a6d+/uWqbwPl9zzTWu/wcGBqpbt25q3769oqKiFBYWppSUFM2bN09//vmnjDGaOHGiLr/8cgUHB+tf//qXdu7c6bYP//znP11j3p7KuFZTpkxxG1uqW7duOv/887VhwwZ9/vnnkqQNGzZozJgx+uWXX4pdx6JFi9S8eXNdeumlWrVqletypqSkJL399tu67777Tppj3rx5rv9XrVpVw4YNO+kykpScnKxLL71UR44ckSQFBwfrmmuuUUREhD755BPt2rVLBQUFmjRpkjp16qS+ffuqf//+ql+/vnbu3KmCggJ9+umnmjhxoqSjY5Z98803rvUX/vlcddVVWrVqlSQpPDxc//jHP1SnTh0tWrRIP/74owoKCnTnnXeqc+fOJQ4N8vvvv6tr164677zzlJGRobp160qSQkND1bdvX7Vp00bVqlVTcHCwkpKSNGvWLG3YsEG5ubkaP3681q9f71rXbbfdpvj4eNfzIUOGqFOnTpo1a5ZmzZpV4mtWGvsBAEBlwnFIxTwOGTVqlMaPH6+MjAylpqZq1qxZGjFihCRpz549WrhwYZHX8nTPF07HHXfc4fr5vfTSS3rjjTdc9wfx8fHRbbfdpq+//vq01rlhwwbXjYt9fHw0duxYNW3aVIcOHdKOHTuK3NR4xowZcjqdkqTmzZtr5MiR8vPz0+7du7Vq1SqtXLnyjPbtVI/3//zzTz3zzDOu5aKjozVu3DilpaXpnXfeUW5u7mlt1+l0avHixa7n7dq1U9OmTYtte/nll7u1/f3331WvXr0i7ebNm6emTZtq5MiR2rt3rz788EMVFBQoKytL11xzjTZu3ChfX1+9//77yszMlCTVqVNHV155pUJDQ7V37179/fffWrJkidt6p06dqg8//FCS5HA4NGLECLVr1047duzQhx9+qLy8PH3++edq37697r///mL34VjmESNGKCQkRImJiWf8mXG6v9fPPvustm3b5jYk4v3336+qVatKkmvs5BMZP3686xxYks4//3x1795dS5Ys0Zw5cyRJCxcu1Pjx4/XOO+8Uu46FCxfqnHPO0aBBgzR//nwtWrRIkrRlyxZ98803Gj16tCTp9ddfdy1zzjnn6MILL1R+fr727NmjpUuXasOGDSfNC1Q4tqvIqJyCg4Nd3361b9++xHZ//fWX2zdlt9xyi2te4W9c/f393b7hO/6SmrfeessYY8y0adNc02666aYi20tPTzfx8fGu5x06dHC1Hzt2rFvbzz77zDXPz8/PJCUlueYV3nZERITZtWtXkW0VvqQnJibG9Y3rsmXLXNN9fX3Nvn37iiy7evVq89FHH5np06ebZ5991jzxxBNu2/ztt99cbU9lqISS2jidTrcezt27d3f7Zviee+5xW+6vv/4q9jWIi4szqampxb6ul156aZE8xSn8LXfXrl1PaRljjHnhhRfcsvzwww+ueQkJCSYsLMw175JLLnHNe+SRR1zTO3Xq5Jpe+OdetWpVk52dbYw5+jMpvJ1ffvnFLceQIUNc84YPH+6afvw3xJdeemmJl/U4nU6zdOlS895775kXX3zRPPvss+auu+5yW/7YZUT79+936w1RuAdGdna2adasmdtyx5zpfgAAUJlxHFJxj0Ouvvpq1zIjRoxwTZ86dapreosWLVzTT/d84USO74mblpZmevToYSSZ0NBQs3LlStcQCxdddFGRY/JT6Ym7cuVKt/04/nL8/Px8tx6nF198sav9J598UiTzgQMH3IZKO9WeuKd6vH/TTTe5pvv4+LgNt3D8+/FUeuImJCS4LTNs2LAS23799ddubadOneqaV7gnbvXq1d2GlHjyySfdljvWO3X8+PGuaU899VSR7R0+fNgcPnzYGHP096d69equ9g899JBb28Lvx6ioKNfv4fHviQYNGpgjR44U2dbpfmYcczq/18acfKiEE7U5dOiQWy/wUaNGuS1X+OpPX19fc+jQoWJfgy5duriGNMzNzTXR0dGueXfddZdrfW3btnVNX7x4cZGcO3bsYDgFVDoUcVEmSruIe+6557otl5+fb/z9/V3z//nPfxpjjFm6dKnbAWXHjh3NlVdeaR5//HEze/Zstz9qGRkZxV4SVNKj8KUyhaffdtttxe5benq6CQ8Pd7X76aefjDHG3Hnnna5pQ4YMcVtmxYoVplWrVifNMnPmTNcyZ1PEXb9+vdv0V1991W25devWuc1/7bXXin0N7r//frflLr/8cte8/v37F/v6HO9MT54KHwzUqFGjyPyRI0e65kdHR7um79y50+3nv3nzZmOMcbvMrfD7sfCYaid7xMTEuJY7/mC1uLG2jDHmp59+MnXr1j3puo8NzfHdd9+5TZ81a5bb+gof6En//1F/pvsBAEBlxnFIxT0OWbBggWuZoKAgV6GxcJGxcDHvdM4XTqa4Iu5//vMf1/PatWu7/j937twzKuJmZWWZqKgo1/SGDRuaESNGmMmTJ5tPPvnEHDx40C3Ts88+62obGBho+vXrZ2688Ubz/PPPmyVLlhQpAp9qEfdUj/c7derkmn7OOee4LZOXl2f8/Pxc820Vca+99lq35Xbt2uW23NNPP22MMebzzz93TfP19TXdu3c311xzjXn66afN/Pnz3Tq/HH9edbLHsSEtjn9PPPfcc8Xu2+l+Zhhz+r/XxpxdEbfw2LzFfS7MmjXLbf6xL72Ofw1mzJjhtlzXrl1d86655hrX9FtvvdU1PSwszJx33nnmlltuMa+88kqJ43gDFZ2PgDJQs2ZN1/93795dYrtdu3aVuFxh0dHRbs99fX0VFRXlep6cnCxJ6tKli6ZNm6awsDBJ0sqVK/XRRx/pwQcf1AUXXKA6deq4Ljk6cuSIjDGnvE/HLrs6XvPmzYudHhoaqlGjRrmez5w5UwUFBfrPf/7jmnbttde6/p+VlaULL7xQ69atO2mWnJycU419QocPH3Z7HhMTc8Lnxy4VPF79+vXdngcGBrr+X1BQcEpZateu7fr/5s2bT/lnU3gfjs97/LTC+evVq6dzzz3X9XzmzJlKSUlxu+tt4Z/P8a/ViZT0XpGKf7/s379fw4YNO+HvyjHHfvbH3vPHxMbGnvD5MaW1HwAAVCYch1Tc45A+ffqocePGkqTs7Gx99dVX2rhxo/766y9Jkp+fn8aOHetqfzrnC2fi0ksvVZ06dSRJ+/btkyS1atVKAwcOPKP1BQUF6bPPPnMNfbF9+3Z9+eWXeuqpp3TFFVeodu3amjZtmqv9hAkTdNVVV8nX11c5OTn69ddfNWPGDE2cOFHdunVT27ZtdeDAgdPOcarH+4XfG8e/D/z8/FS9evXT2m5UVJQCAgJcz48/fyzsTM8tj//dPbYPl112mSZNmqTAwEDXsA7vvvuu7rvvPvXv31+NGjVynbudzntbOv1zy9P9zDiT3+uzVd7nllOmTNEFF1wgSUpPT9fcuXP12muv6bbbblPbtm3Vr18/ZWRknPZ+AJ6MMXFRJnr37q3t27dLOvph/ssvv7j90Tmm8Fisx5YrTmJiottzp9OppKQk1/MqVaq4/j9hwgTdeOONWrJkidatW6ctW7boxx9/1JYtW3To0CGNGzdOu3btcltGki6++OISty9JHTt2LHZ6aGhoictcc801evvttyVJX3/9tUaNGqX9+/dLkqpXr66LLrrI1fa3335zO6CaOHGi7rvvPlWvXl2ZmZkn3M6ZqlatmtvzhISEEz4/Nh7S8fz9/d2eOxyO084yYMAAbdmyRdLRP+j//e9/T2k8usL7cHze46cdn/+aa65xjYH3ySefqG7duq6DmLZt26pTp07FbkeSHnvsMQUHB5803/GK+zl+9913rvG2JOn555/Xddddp8jISK1fv16tWrUqsszx79/jf0cKj1FXWGntBwAAlQnHIRX7OOTqq6/WAw88IOnoa3nsPESSLrjggiLFo1M9XzgTfn5+uuWWW9zGPL399tvPaF3HnHvuudqxY4dWrlypVatWaevWrfrjjz/0+++/Kzc3V3fffbcuvvhiNW7cWH5+fvrggw/0/PPP648//tCmTZu0adMmff311zpy5Ij+/vtv3XfffXr//fdPK8OpHu8Xfm8c/77Iz8/XoUOHTmu7vr6+6t69uxYsWCBJWrNmjbZu3eoq3Bd2pueWx//uFt6HZ599Vg888ID++OMPbdy4UZs3b9a3336r/fv3a9euXbrlllu0YMGCIu/tcePGnXAM2eMLlcec7NzyVD8zzuT3+myV97llRESEfvjhB+3du1dLlizR5s2btX79en399dfKzMzUggULNHXqVD366KOnuyuA57LcExiV1KJFi9wuiejcubPbGErGHL0Mo/CYOS1btnQbs+ZMxsTdt29fsWNYFR5LSpJr/J3Cd8bt2bOna+ydwpKTk4uMJ1V4XYUvgypO06ZNXW0bNGjg+v+xuwGXtE8rV650zTv+UrjC29y7d+8JL1sxpvTGxF21atUpvQYnuiyrJH///bfb+yE2NtZte8fk5uaat956yyQkJBhjjHnxxReLvSzHmKJj0R1/+VVmZqaJjIws9ufzwgsvuLVdtWqV23YKDy1x/H4Uvqvt8T+74hw/DtexsbWMKXqZ3rGf3fFj0RUe0/lEY9Gd6X4AAFCZcRxSsY9D9uzZ4xp71s/Pz8TFxbm28fXXX7u1Pd3zhRMpbjgFY46ODXpseLmqVau6xqA90+EU1q9fX2TbBQUFbu+fL774whhjzMaNG93GvD2m8FjArVu3dk0/1eEUTvV4v/CYuL6+vmbLli2ueWcyJq4xRc+TLrzwwiLnbR988IFbm8GDB7vNP35M3JSUFNe8ksbE3b59e7Hj03711VeutmFhYcaYo+dVhYe9OH482GMSEhLMV1995Xp+KkPjHXM6nxln8nttjDELFy50m7du3boiOcp6TNzjX4PCdYFx48a5pq9du7bY8/fCYxlfeOGFJb6eQEVET1yUiR49euimm27Sm2++KUlavny5WrRooVGjRql69epau3atvvjiC9edUwMCAjRjxgz5+BQ/wkdeXp569uypq666Smlpaa7erZIUGRmpkSNHSjram3XMmDHq1auXWrRooVq1asnpdOqrr75ytQ8ICFBISIgk6e6779aYMWMkHb3jatu2bXXRRRepatWqSkpK0l9//aWFCxeqZs2arrtgnq5rrrlGkydPliTt2LHDbXphzZo1c3t+5ZVX6vLLL9fOnTtddzktTo0aNeTv76+8vDxJ0r/+9S+tXr1a/v7+6tevnzp37lzisj4+Prrzzjv14IMPSpIWL16sXr166fzzz9fGjRvdvs3u37+/2rVrd4p7ffpatWqlxx9/3NVrIT4+Xp07d9aFF16oDh06yOFwaOvWrZozZ44SEhJcl6SNGzdOjz/+uKtn9ogRI3TttdcqIiJCM2fOVHp6uqSj3+BOmDDBbZvBwcEaPXq063167Ofj7+/vel8c065dO5133nmaO3eupKN3ZJ49e7Y6deokHx8f7dq1S3/88Yc2bNighx9+WL169TrlfT/+Zz906FBdcMEFWrNmjb744otil6lZs6aGDh2q77//XpL0wQcfKCUlRe3atdP333+vTZs2FbtcWe4HAAAVFcch/68iHofUqVNH5513nubMmeO6O7109LL5oUOHurU93fOFMxEVFaWffvpJhw4dUs2aNc9qXcnJyWrZsqVatWqlLl26qFatWgoODtbChQuVkpLianes9+gLL7ygDz/8UAMGDFCDBg0UExOjw4cP64MPPijStixcd911mjFjhowxcjqd6tOnj8aOHavU1FS3c7jTMXr0aH300UeaPXu2JOn7779X69atNXz4cIWFhWnp0qWu96J0tIfn9OnTS1zfoUOHdM4552jkyJHau3ev27lWo0aN1L9/f0nSf/7zHz388MPq16+fmjRpopo1ayojI0OffPKJq/2x19LHx0d33XWX/vWvf0k62it4+/btOu+88xQeHq74+HgtX75cS5cuVa9evTR8+PDTfh1O5zPjTH6vJfehZSTp1ltv1aBBg+Tn56eLL75YTZs2LXHZqKgoXX311a6f82effabk5GR1795dS5Ys0Zw5c1xtx44d6zY84pmYNGmSli1bpgEDBiguLk41atTQ/v379e6777ralOV7HbDCdhUZlVdeXp657bbb3L5VK+4RFRVl5syZU2T5wt+4dezY0e0mYccePj4+br1kP/nkk5Nur/AdLY0xZvLkySddpl69em7LFJ53sp64+/btc/tG8tj+FGfw4MHFbr/wN93FbbPwgPaFH88++6wx5sTfbubn57vddKO4R4sWLcy+fftO+TU4k564x0yfPt0EBgae9GdSuGf2ggULTJUqVUps6+PjU+JNApYuXVqkfeE77BaWkJDg1nu7pEfhXgWn0gMmNzfXtGnT5pR+9oV/djt27DCxsbFFlnE4HG7vJYfDcdb7AQCAN+A4pOIehxS+odixx/HH/cac2flCSUrqiVuSM+mJe+DAgZPm7dKli8nLyzPGuPeELen9WLh3cmn3xDXGmPvuu6/YbXfs2NHExMS4nj/66KOn9DobY0xaWtpJz1kkmfr16xd7A7/CPXF79OjhdpPsY4+goCCzYMEC1zJPPfXUSbf30ksvudo7nU5z1VVXnXSZwq/X6fTENebUPzPO9PfaGPebAhZ+fP7558aYE9/8LD093fTp0+eE+9+zZ0+335Uz7Yk7aNCgE24nKCjILFu27ISvJ1DRUMRFmfvrr7/MzTffbFq2bGnCw8ONn5+fqVGjhunXr5+ZOnWqSU5OLna54z+sN2zYYIYPH26qVq1qgoODTY8ePcyPP/7otsyePXvMk08+aYYOHWoaNWrktr0BAwaY9957r9htLVq0yFx55ZWmQYMGJjAw0Pj7+5vatWub888/30yZMsVs3brVrf2JDmiKM2TIELdlXn755WLbZWRkmAkTJpiaNWuagIAA07hxYzNlyhSTn59/wm0eOnTIjBs3zsTExLguJZNOrYh7zOeff26GDBlioqOjjZ+fn4mMjDRdu3Y1zz77rElPTy/S/kwP6k7F/v37zSOPPGJ69eplatSoYfz8/ExISIhp0aKFufnmm82vv/5a5M66e/fuNRMnTjStWrUyISEhJiAgwNStW9eMGTPGLFmy5ITba9Wqldv+fPfddyW2zcnJMa+//ro599xzTfXq1Y2vr68JDQ01zZs3N1deeaX5+OOP3V6vUzl5MsaYgwcPmquvvtpERUWZwMBA07p1azNjxoyT/ux27txpRo8ebapUqWKCg4NN9+7dzQ8//OB20Fm1atWz3g8AALwFxyEV8zgkOzvbbZgwSWbt2rVF2p3p+UJxyqOIm52dbV555RVzxRVXmJYtW5pq1aoZX19fExERYTp37mwef/xxt+0uW7bM3HvvvaZPnz4mLi7OBAUFmYCAABMXF2dGjhxZZJiKsijiGmPMjBkzTKtWrUxAQICpWbOmue2228yRI0dcw0xIMtOnTz/pa3y8X3/91Vx99dWmSZMmJjQ01Pj7+5vY2FgzePBg8/rrr5vMzMxilytcxH344YfN4sWLzXnnnWfCw8NNaGioOe+884oU/DZs2GAeeughM3DgQFO/fn0TEhJi/Pz8TM2aNc3QoUPNt99+W+y2Zs2aZUaMGGHq1KljAgICTGBgoKlXr5656KKLzIsvvmj279/vanu6RVxjTv0z40x/r3fs2GGGDx9uqlWr5jZsyqkUcY052pnr3//+t+nfv7+pVq2a8fPzM1WrVjV9+/Y1b775pusLh1N9DUoq4v7888/mjjvuMN26dTO1a9d2vdYNGzY048aNM2vWrDnpawlUNA5jTvHWqwAAj1JQUKD8/Hy3O/ZKR2/816NHDy1btkySdN555+mnn36yEREAAFRSHIegJFlZWcXesO777793u7HzokWL1KNHj/KMBgAVGmPiAkAFlZqaqiZNmugf//iH2rdvr+joaO3bt0/vvfee68RJksaPH28xJQAAqIw4DkFJ7r//fq1atUoXXXSRGjRooPz8fC1fvlyvvfaaq03nzp3VvXt3iykBoOKhiAsAFdihQ4f00ksvFTvP4XDo0Ucf1YUXXljOqQAAgDfgOATFMcbo119/1a+//lrs/MaNG+vzzz+Xw+Eo32AAUMFRxAWACiokJESTJ0/W/PnztX37dh05ckT+/v6Ki4tTr169dNNNN+mcc86xHRMAAFRCHIegJMOGDVNCQoKWLl2qgwcPKjs7W1WqVFHr1q01fPhwXX/99QoJCbEdEwAqHMbEBQAAqIR+++03Pfvss1qxYoUOHDigr7/+WsOGDXPNN8bo4Ycf1ltvvaXk5GT17NlTr7/+upo0aeJqc/jwYd1+++367rvv5OPjoxEjRmj69OkKCwuzsEcAAACA9/KxHQAAAAClLyMjQ+3atdOrr75a7PypU6fqpZde0htvvKGlS5cqNDRUgwYNUnZ2tqvNmDFjtG7dOs2dO1fff/+9fvvtN914443ltQsAAAAA/oeeuAAAAJWcw+Fw64lrjFGtWrU0ceJETZo0SZKUkpKimJgYvffeexo9erQ2bNigli1b6s8//1Tnzp0lST/++KOGDBmivXv3qlatWkW2k5OTo5ycHNfzgoICHT58WFFRUYx9CAAAABzHGKO0tDTVqlVLPj4n7mvLmLgAAABeZseOHYqPj9fAgQNd0yIjI9W1a1ctXrxYo0eP1uLFi1WlShVXAVeSBg4cKB8fHy1dulTDhw8vst6nnnpKjz76aLnsAwAAAFBZ7NmzR3Xq1DlhG4q4AAAAXiY+Pl6SFBMT4zY9JibGNS8+Pl7R0dFu8/38/FStWjVXm+NNnjxZd911l+t5SkqK6tatqz179igiIqI0dwEAAACo8FJTUxUXF6fw8PCTtqWICwAAgFIRGBiowMDAItMjIiIo4gIAAAAlOJWhx7ixGQAAgJeJjY2VJCUkJLhNT0hIcM2LjY1VYmKi2/z8/HwdPnzY1QYAAABA+aCICwAA4GUaNGig2NhYzZs3zzUtNTVVS5cuVffu3SVJ3bt3V3JyslasWOFq88svv6igoEBdu3Yt98wAAACAN2M4BQAAgEooPT1dW7dudT3fsWOHVq1apWrVqqlu3bqaMGGCnnjiCTVp0kQNGjTQgw8+qFq1amnYsGGSpBYtWmjw4MG64YYb9MYbbygvL0+33XabRo8erVq1alnaKwAAAMA7UcQFAACohJYvX67+/fu7nh+74di4ceP03nvv6Z577lFGRoZuvPFGJScnq1evXvrxxx8VFBTkWubjjz/WbbfdpgEDBsjHx0cjRozQSy+9VO77AgAAAHg7hzHG2A4BAACAyic1NVWRkZFKSUnhxmYAAADAcU7neJkxcQEAAAAAAADAg1HEBQAAAAAAAAAPRhEXAAAAAAAAADwYRVwAAAAAAAAA8GAUcQEAAAAAAADAg1HEBQAAAAAAAAAPRhEXAAAAAAAAADwYRVwAAAAAAAAA8GAUcQEAAAAAAADAg1HEBQAAAAAAAAAPRhEXAAAAAAAAADwYRVwAAAAAAAAA8GAUcQEAAAAAAADAg1HEBQAAAAAAAAAPRhEXAAAAAAAAADwYRVwAAAAAAAAA8GAUcQEAAAAAAADAg1HEBQAAAAAAAAAPRhEXAAAAAAAAADwYRVwAAAAAAAAA8GAUcQEAAAAAAADAg1HEBQAAAAAAAAAPRhEXAAAAAAAAADwYRVwAAAAAAAAA8GAUcQEAAAAAAADAg1HEBQAAAAAAAAAPRhEXAAAAAAAAADwYRVwAAAAAFV56eroefvhhNW/eXMHBwapVq5ZuvvlmHTlyxNUmLS1Nd955p+rUqaOAgAA1atRIjz76qPLz80+6/oSEBF177bWKjo5WYGCgWrZsqVdeecWtzbvvvquGDRsqLCxM/fv31+bNm93mDx06VIMGDSqdHQYAAF7FYYwxtkMAAACg8klNTVVkZKRSUlIUERFhOw4quf79++vXX3+Vr6+vWrVqpR07digtLU2dO3fW4sWL5ePjo3PPPVcLFiyQv7+/GjZsqC1btqigoEBXXXWVPvjggxLXnZGRoU6dOmnTpk0KDg5WnTp1tGXLFknSgw8+qMcee0wbN25Uq1atNHbsWD355JNq06aNmjdvrkWLFkmSZs6cqRtuuEF///23GjRoUC6vCQAA8Gync7xMT1wAwCm7+uqrNWzYsFJf73vvvacqVaqU+noBAN5h/fr1+vXXXyVJ06dP1+rVq7VixQpJ0vLly/XZZ5/pm2++0YIFCyRJX331lTZu3KgXX3xRkvThhx9q5cqVJa7/zTff1KZNm+RwOLRkyRJt3rxZd911lyTp6aefVkJCgtauXauCggL16NFDtWrVUrNmzbR69WpJUlJSkiZMmKDHH3+cAi4AADgjFHEBoIz069dPEyZMKDLdUwqWJeUDAKCiKSgocP3fx8fH7V9J+vnnnzV79mxJUnBwsIYMGSJJGjFihKvNjz/+WOL6jy3bpEkTtW3b1m3ZvLw8zZs3T23atJGPj4/++OMP7d+/X5s2bVK7du0kSXfddZfq16+vO+6446z3FQAAeCeKuAAAAAAqtBYtWqh169aSpNtvv13t27dXx44dXfP37dunPXv2SJKioqJcBd6YmBhXm927d5e4/mPLRkdHu6Ydv2zz5s3173//WwsWLFCTJk3Upk0bvfPOO/r555/1ySef6PXXX9c999yjmjVrqm7dunrmmWdKYc8BAIC3oIgLABYdG57gueeeU82aNRUVFaVbb71VeXl5rjb169fXE088obFjxyosLEz16tXTt99+q4MHD+qSSy5RWFiY2rZtq+XLl7uWSUpK0hVXXKHatWsrJCREbdq00SeffOK23QULFmj69OlyOBxyOBzauXOnJGndunW68MILFRERofDwcPXu3Vvbtm1zy32ivDk5OZo0aZJq166t0NBQde3a1XWJ6zHvvfee6tatq5CQEA0fPlxJSUml+KoCALyNr6+vZs+erTFjxqh69eravn27evfurUaNGkmS/P39i13ubG4PUtyy11xzjbZv366MjAz9+uuviouL00033aRJkyZp2bJlmjZtmm6//XZdfPHFuu+++07Y+xcAAKAwirgAYNn8+fO1bds2zZ8/X++//77ee+89vffee25tXnjhBfXs2VN//fWXhg4dqquuukpjx47VlVdeqZUrV6pRo0YaO3as64QyOztbnTp10qxZs/T333/rxhtv1FVXXaVly5ZJOjpeYPfu3XXDDTfowIEDOnDggOLi4rRv3z716dNHgYGB+uWXX7RixQpde+21bnftPlne2267TYsXL9ann36qNWvWaOTIkRo8eLDrBjBLly7Vddddp9tuu02rVq1S//799cQTT5TtiwwAqPTq1Kmjjz76SPHx8UpNTdUXX3yhQ4cOSZKaNWumuLg4SdKhQ4dcwy8kJia6lq9bt26J6z62bOH2p7LsQw89JD8/Pz300EP6+eefJR3tKXzTTTdJkubOnXva+wkAALwTRVwAsKxq1ap65ZVX1Lx5c1144YUaOnSo5s2b59ZmyJAhuummm9SkSRM99NBDSk1N1TnnnKORI0eqadOmuvfee7VhwwYlJCRIkmrXrq1Jkyapffv2atiwoW6//XYNHjxYn332mSQpMjJSAQEBCgkJUWxsrGJjY+Xr66tXX31VkZGR+vTTT9W5c2c1bdpU11xzjZo1a3ZKeXfv3q13331Xn3/+uasH1KRJk9SrVy+9++67ko4WkAcPHqx77rlHTZs21fjx4zVo0KDyeKkBAJXYypUrlZaWJklyOp26++67lZKSIkm6/PLLNXjwYElHv+j84YcfJElffvmla/lj87/++ms1b95czZs31759+9zmbdmyRWvWrHFb1t/fXwMGDCg2z/Tp0zVjxgwFBQW5vmgNCAgosWcwAABASfxsBwAAb9eqVSv5+vq6ntesWVNr1651a3PsJirS/4/B16ZNmyLTEhMTFRsbK6fTqSlTpuizzz7Tvn37lJubq5ycHIWEhJwwy6pVq9S7d+8TnlyeKO/atWvldDrVtGlTt2VycnIUFRUlSdqwYYOGDx/uNr979+5cUgoAOCvvvPOO3n77bTVu3Fjx8fGuXrgTJkxQly5d1KlTJ/Xq1UsLFy7UpZdeqkaNGmnz5s2SpH/84x+uMXRTUlK0adMmSXINF3TTTTfpzTff1JYtW9StWzfFxcW5lr377rvdxseVpPz8fF1//fW65ppr1LdvX0nSwIED9fXXX+uHH37Q9u3bJanY4i8AAEBxKOICQBmJiIhw9QAqLDk5WZGRka7nxxdMHQ6H2122j2/jcDhKnHZsuWeffVbTp0/Xiy++qDZt2ig0NFQTJkxQbm7uCTMHBwefdL9OlDc9PV2+vr5asWKFW6FXksLCwk66bgAAzlSXLl00f/58bd++XcYYderUSTfffLOuu+46SUfHzZ01a5YefPBBffHFF9q2bZvq1q2rsWPH6oEHHjjhusPCwrRgwQJNnjxZs2bN0o4dO9S8eXP985//1B133FGk/bRp0xQfH69nn33WNe3GG2/Upk2bdMMNN8jf319PPPGEhgwZUrovAgAAqLQo4gJAGWnWrJl++umnItNXrlxZpKdqaVu0aJEuueQSXXnllZKOFnc3b96sli1butoEBATI6XS6Lde2bVu9//77ysvLO6NLPTt06CCn06nExET17t272DYtWrTQ0qVL3aYtWbLktLcFAEBhY8eO1dixY0/YJiIiQtOnT9f06dNLbHP11Vfr6quvLjK9Zs2aRcasL8k999yje+65x22an5/fSbcNAABQEsbEBYAycvPNN2vz5s0aP3681qxZo02bNmnatGn65JNPNHHixDLddpMmTTR37lz98ccf2rBhg2666SbXeLnH1K9fX0uXLtXOnTtdN3m57bbblJqaqtGjR2v58uXasmWLPvzwQ9dlpSfTtGlTjRkzRmPHjtVXX32lHTt2aNmyZXrqqac0a9YsSdL48eP1448/6rnnntOWLVv0yiuvMJQCAAAAAAAnQBEXAMpIw4YN9dtvv2njxo0aOHCgunbtqs8++0yff/656wYpZeWBBx5Qx44dNWjQIPXr10+xsbEaNmyYW5tJkybJ19dXLVu2VI0aNbR7925FRUXpl19+UXp6uvr27atOnTrprbfeOq1eue+++67Gjh2riRMnqlmzZho2bJj+/PNP1527u3XrprfeekvTp09Xu3bt9NNPP530MlYAAAAAALyZwxy7TSoAAABQilJTUxUZGamUlBRFRETYjgMAAAB4lNM5XqYnLgAAAAAAAAB4MIq4AAAAAAAAAODBKOICAAAAAAAAgAfzsx0AgGfIN05lFeQq05mjrILcow9nrrIKjj7PM04ZGRUYIyOjlgl5qpWQJvn6yuHjK/n4HH0ce+7rK5/QMDkiIuUTESmf4BDbuwgAAAAAAFAhUcQFKimnKVBiXooO5aUqKS9dh/PTdDgvXUn5aUrKS9Ph/HQl5aXpSH66Mpw5yjX5p7X+V5cEKPTHRae+QECAfCKqHC3ouh5VXP+6ir2Rhab58REFADh77VbcZTsCAC+2utM02xEAAJUAFRKgAkt3ZmtPziHtzUlye+zLTdKBnCPKV4HtiP8vN1cFhxJVcCjxlBfxqVpNfnXqybdOXfnVqS+/OnXlF1dPvjViyjAoAAAAAACAZ6GIC1QAmc4cbck6oM1Z+7Ul64C2ZB3Q9qwEJTszbEcrUwVHDiv3yGFp7V9u0x3BwfKtXfdoUbdwcbdWHL13AQAAAABApUO1A/Aw+3MOa13mnqMF28yjhdv9uUdkZGxH8xgmK0v5Wzcpf+sm9xm+vvKNrSW/OvWOPho0VkDLNvTcBQAAAAAAFRpFXMCi3IJ8bcjcq1UZO7UmfadWZ+zUwbxU27EqLqdTzn175Ny3RzlLF7om+0THKqBVOwW0aquAlu3kF1fPYkgAAAAAAIDTQxEXKEfJ+RlanrZNq9J3aE3GLm3I3HvaNxTD6StIjFd2Yryy58+RJPlUqSr/Fm0V0LqdAlq2lV/DJnL4+FhOCQAAAAAAUDyKuEAZyivI18r0HVqStklLUjdrY+Y+FTAsgnUFyUeUs3iBchYvkCQ5QkLl37z10Z66rdrLv2lzOfwDLKcEAAAAAAA4iiIuUMq2ZB3QktTNWpy6SSvStyu7INd2JJyEycxQ7sqlyl259OiEgAD5N2mhwHadFNi1l/wbNrEbEAAAAAAAeDWKuMBZyjNO/Zm2VfOOrNGClHWMaVsZ5OYqb91q5a1brfSZ78gnOlZBXXspsGsvBbRuJ4cvH50AAAAAAKD8UIkAzkB2Qa4WpWzUvOS1+i1lvdKcWbYjoQwVJMYr87svlPndF3KERyiwc3cFde2lgE5d5RMUbDseAAAAAACo5CjiAqcozZmlBcnrNC95rf5I3cQwCV7KpKUqe/4cZc+fI0dgkALP6aGgXv0VeE53OQICbccDAAAAAACVEEVc4ATyCvK1IGW9Zh1eod9T1ivPOG1HggcxOdnKXviLshf+IkdwsAK79FJQ73MV2LGrHP7+tuMBAAAAAIBKgiIuUIxV6Tv0XdJy/XRklVIZKgGnwGRlKXvBXGUvmCtHaJgCu/ZSyHlDFdC6ve1oAAAAAACggqOIC/xPQm6yvk9arv8m/aldOQdtx0EFZjLSlf3Lj8r+5Uf51WuokAuGKaj/IPmEhNiOBgAAAAAAKiCKuPBqxhgtTN2g/yQu0qLUjSqQsR0JlUz+ru1KfWOa0t5/Q0H9zlfI0OHyr9fQdiwAAAAAAFCBUMSFV0rNz9TXScv0+cFF2pOTZDsOvIDJylTW7G+UNfsb+bdqp5AhwxTUo58cfnwMAwAAAACAE6N6AK+yMXOfPk1cqNmHVyrb5NmOAy+Vt261UtatVlqVlxV8/oUKGXyxfGvE2I4FAAAAAAA8FEVcVHr5xqm5R9bo08TftSpjp+04gEtB8mFlfPaBMr74WIHndFfIkOEK6HCOHA6H7WgAAAAAAMCDUMRFpZVdkKtvDi3T+wm/an/uYdtxgJIVOJWzdKFyli6Ub606CrngEgWff5F8QkJtJwMAAAAAAB6AIi4qnTRnlj5LXKSPEn/T4fx023GA0+Lcv1dpb7+q9P98oNBhoxVy8WXyCQ6xHQsAAAAAAFhEEReVRlJemj5KXKDPEv9QekG27TjAWTHpaUr/6C1lfPuZQi+9QqFDR8gRFGQ7FgAAAAAAsIAiLiq8+Nwjeif+F31zaKlyTL7tOECpMqkpSn/vDWV+85lCR/xDIUOGyREQaDsWAAAAAAAoRxRxUWEdzkvXv+Pn6vODi5VL8RaVXEHyYaW9/Yoyvv5UoSOvVMigi+Xw97cdCwAAAAAAlAOKuKhw0p3Z+iDhV32UsEAZBTm24wDlquDwIaW9+aIyvpypsFFjFXzeUDn8+CgHAAAAAKAy48wfFUZOQZ7+c3CR3omfpyP5GbbjAFYVHEpU6mvPKeOLjxU6epyCzx0khy8f6QAAAAAAVEac8cPjOU2B/pu0TG/s/0kJecm24wAexZl4QKkvPa2Mzz9S2OhxCup7nhy+vrZjAQAAAACAUkQRFx5taepmPb3na23PTrAdBfBozgN7lfLCk0r/4iNF3DRBge06244EAAAAAABKCUVceKR9OYf13N7/6pfktbajABWKc88uHXngTgX1Gajw62+Tb9Uo25EAAAAAAMBZoogLj5JdkKu34+fp/fj5yjH5tuMAFVb2bz8rZ8UShY25TiFDL5XDx8d2JAAAAAAAcIYo4sJj/HRklabt/U4Hco/YjgJUCiYjXWkzpitr3mxF3jJJ/k1b2I4EAAAAAADOAEVcWLc1K15P7/lKf6ZttR0FqJTyt21W0t3/VPCgixU+9kb5hIXbjgQAAAAAAE4DRVxYk1eQr3/H/6y34+cpzzhtxwEqt4ICZc3+RjmLf1P4tbcouP8g24kAAAAAAMApoogLK9Zk7NIjO/+jbdnxtqMAXqUg+bBSpj2hrJ9/UMQ/75JfXD3bkQAAAAAAwElwpxuUq0xnjp7d843GbXyJAi5gUe6alTo0/hqlffCmTE6O7TgAAAAAAOAEKOKi3CxJ3azL1j+rjxJ/U4GM7TgA8vOU8flHOnTLVcr+8w/baQAAAAAAQAkYTgFlLs2ZpWf3/Ff/TVpmOwqAYjgTDyj5sXsVfN5Qhd94h3yCgm1HAgAAAAAAhdATF2VqZdp2jVr/HAVcoALImjtLSeOvVe7m9bajAAAAAACAQijiokzkG6de2feDrtv8qvbnHrEdB8Apch7Yq8P33KL0/7wv43TajgMAAAAAAEQRF2Vgd/ZBjdv4st6K/5mxb4GKyOlU+kf/1uHJtys/4YDtNAAAAAAAeD2KuChVXx9aqss3TNPfmbttRwFwlvI2rFXS+Gu0ZeNi21EAAAAAAPBq3NgMpSI1P1OP7fpcc5NX244CoBRlNairy9M+15AdO3R/3REK8Q20HQkAAAAAAK9DT1yctb8zdmvUhucp4AKVTUSkJl0YJaeP9N3h5frHxhe0OXO/7VQAAAAAAHgdirg4K58f/EPXbHpFB7h5GVDpfDaqs7YE57ie78hO1JUbX9RXh5ZYTAUAAAAAgPdhOAWckZyCPD25+0v9N2mZ7SgAysCeAd31dp3UItNzTL4e3fWZ/s7Yrclxl8rfhz8jAAAAAACUNc6+cdr25iRp4rb3tDFrn+0oAMqAs149je9RcMI2Xx5aoq1Z8Xq+0dWq4R9RTskAAAAAAPBODKeA07IwZYP+seEFCrhAZRUYpMdHNFCm74mLuJK0OmOnrtgwTavSd5RDMAAAAAAAvBdFXJwSY4ze2D9Ht2/9t1KcmbbjACgjC0Z01+KIjFNufzAvVddtfk2fHVxUhqkAAAAAAPBuFHFxUlkFuZq0/X29fmCOCmRsxwFQRpK7dNCUpumnvVy+cerJ3V/q4Z2fKrcgvwySAQAAAADg3Sji4oQSc1N07aZX9HPyGttRAJQhEx2t8ecFn9U6vklapus3v6Yj+adfCAZQ/pxOpx588EE1aNBAwcHBatSokR5//HEZ8/9f2Bpj9NBDD6lmzZoKDg7WwIEDtWXLFoupAQAAAO9EERcl2pC5V2M2vqj1mXttRwFQlnx99drI1krwzzvrVa3O2KmxG1/SruyDpRAMQFl65pln9Prrr+uVV17Rhg0b9Mwzz2jq1Kl6+eWXXW2mTp2ql156SW+88YaWLl2q0NBQDRo0SNnZ2RaTAwAAAN6HIi6KtSB5na7Z9IoS81JsRwFQxv4e2lPf1kgrtfXtzjmksRtf4oZngIf7448/dMkll2jo0KGqX7++LrvsMp1//vlatmyZpKO9cF988UU98MADuuSSS9S2bVt98MEH2r9/v7755hu74QEAAAAvQxEXRXyauFB3bntXWQW5tqMAKGPZLZvrnvZZpb7eZGeGbtz8uuYeWV3q6wZQOnr06KF58+Zp8+bNkqTVq1dr4cKFuuCCCyRJO3bsUHx8vAYOHOhaJjIyUl27dtXixYuLXWdOTo5SU1PdHgAAAADOnp/tAPAcxhg9v/dbfZi4wHYUAOUhPEL3XFhdTp+cMll9jsnX3ds/0J21L9S42P5lsg0AZ+6+++5TamqqmjdvLl9fXzmdTj355JMaM2aMJCk+Pl6SFBMT47ZcTEyMa97xnnrqKT366KNlGxwAAADwQvTEhaSjd5f/186ZFHABL/LFqHO0KaRsCrjHGBlN2/edpuz+Uk5TUKbbAnB6PvvsM3388ceaOXOmVq5cqffff1/PPfec3n///TNe5+TJk5WSkuJ67NmzpxQTAwAAAN6LnrhQTkGe7tn+gX5NWWc7CoBysu/cbnorrvwuc/7PwUU6mJeqqQ2ukr8Pf3oAT3D33Xfrvvvu0+jRoyVJbdq00a5du/TUU09p3Lhxio2NlSQlJCSoZs2aruUSEhLUvn37YtcZGBiowMDAMs8OAAAAeBt64nq5DGe2btkygwIu4EWc9epqfA9T7tv9JXmtxm97W9mMtw14hMzMTPn4uB8K+vr6qqDgaK/5Bg0aKDY2VvPmzXPNT01N1dKlS9W9e/dyzQoAAAB4O4q4Xiw5P0M3bH5dy9O32Y4CoLwEBumJEQ2U7mdnaIM/Ujfpli0zlOHMtrJ9AP/voosu0pNPPqlZs2Zp586d+vrrrzVt2jQNHz5ckuRwODRhwgQ98cQT+vbbb7V27VqNHTtWtWrV0rBhw+yGBwAAALwM17R6qcTcFN205Q1tz06wHQVAOfpteHf9EZFuNcOK9O26cfMber3JjYrwC7GaBfBmL7/8sh588EHdcsstSkxMVK1atXTTTTfpoYcecrW55557lJGRoRtvvFHJycnq1auXfvzxRwUFBVlMDgAAAHgfhzGm/K+phVV7cg7pps1vaF/uYdtRUIG9uiRAjX9cZDsGTkPKOR00aqjnfHfXNLim3mjyT0X5h9uOAqCMpKamKjIyUikpKYqIiLCWo92Ku6xtGwBWd5pmOwIAwEOdzvEywyl4md3ZB3XNplco4AJexlSvofHnBduO4WZz1gFds+kVJeQm244CAAAAAIBHo4jrRfbmJOn6za/rYF753ZEegAfw8dUbl7dRfECe7SRF7Mo5qKs3vaJ9OXyxBAAAAABASSjieon9OYd1w+bXlJCXbDsKgHK2fmhPfVMjzXaMEu3PPawbN79Oj1wAAAAAAEpAEdcLxOce0fWbX9P+3CO2owAoZzktmunuDtm2Y5zU3twk3bj5DSXleW6xGQAAAAAAWyjiVnKJuSm6YfPrjIELeKOwcN17UbTyfSrG/St35iTqpi1vKDk/w3YUAAAAAAA8CkXcSuxQXqqu3/yaduccsh0FgAVfjeqiDSGe3wu3sC1ZB3TzljeV5syyHQUAAAAAAI9BEbeSOpyXrhs2v65dOQdtRwFgwf7+3fRm3Yp5E8P1mXt165a3lOnMsR0FAAAAAACPQBG3Esp05ui2rW9pe3aC7SgALHDGxen2nhVjCIWSrM7YqfHb3lZ2Qa7tKAAAAAAAWEcRt5LJN05N2v6+1mXusR0FgA0BgZpyWSOl+xXYTnLW/kzbqru3fyCnqfj7AgAAAADA2aCIW8k8uuszLUrdaDsGAEsWDu+hhZGV58Zgv6Ws15TdX9qOAQAAAACAVRRxK5Hp+77Xt0l/2o4BwJLUzu31eIt02zFK3ReHFuvtAz/bjgEAAAAAgDUUcSuJmYm/6534X2zHAGBL9eoaf36w7RRl5uX9szUraYXtGAAAAAAAWEERtxL46cgqPbvnG9sxANji46sZo9rqQEC+7SRlxsjo4V2famnqFttRAAAAAAAodxRxK7iVadv1rx0zVaCKfSd6AGdu45Ae+jI6zXaMMpdnnJq4/V1tyTpgOwoAAAAAAOWKIm4Fti/nsO7a/q5yTeXtfQfgxHKaN9Wkjjm2Y5SbNGe2bt3ylhJyk21HAQAAAACg3FDEraAynTmasO1tHcmvPHehB3CawsI0+eJY5fl4V0/8hLxkjd/6trILcm1HAQAAAACgXFDErYCMMfrXzpnazCXFgFf7ZmRXrQvJsh3Dio1Z+/Tors9sxwAAAAAAoFxQxK2AXjvwo35JXms7BgCLDvTrqtfrpdqOYdUPh1fq/fj5tmMAAAAAAFDmKOJWMD8dWaW3DvxsOwYAiwri4jS+l+0UnmH6vllanLrJdgwAAAAAAMoURdwKZGPmPj2481MZedf4lwAKCQjQU5c1Uqpfge0kHsGpAt27/UPtzUmyHQUAAAAAgDJDEbeCOJyXrgnb3uFGPoCX+2N4T/0WyQ0NC0txZmrCtneU6cyxHQUAAAAAgDJBEbcCMMbo/p0f60DuEdtRAFiU1qmdHm2RbjuGR9qSdUAP7fzUdgwAAAAAAMoERdwK4N/xPzPmI+Dtoqpr/PmhtlN4tLnJq/Ve/C+2YwAAAAAAUOoo4nq4FWnb9Pr+ObZjALDJx0dvjWqr/YF5tpN4vJf3z9aajF22YwAAAAAAUKoo4nqww3npunfHh3KKGxgB3mzTBT31RUya7RgVQr5x6r7tHyrNmWU7CgAAAAAApYYirocyxuhfOz/WwbxU21GAMrc4KUVjl61T+7nLVPP7hZodn+Q23xijqZt2qd3cpWrwwx8atWSttqefvEj37s79Omfen6r/wyINWbhKfx1xL4Q+vG67WsxZok4/L9OXexPd5n23/5DGLlt39jt3lnKbNdHETtyw63Tsyz2sx3Z9bjsGAAAAAAClhiKuh3o7fp7+YBxceIlMp1MtI8I0pXXDYue/um2f3t6xX8+0aaxZvdopxNdXVyz7W9nOknup/3f/QT2yfocmNq2rOb07qGVEqK5Y9rcO5eRKkn5KSNLX+w/qk66t9ECLBpq0ZquSco8OV5Cal6+nN+3UlDaNSn9nT0domCZfEqs8H2M3RwX005FV+uLgYtsxAAAAAAAoFRRxPdDKtO16bf+PtmMA5WZAdDXd17yehtSsXmSeMUZv7dinCU3iNDg2Si0jQvVS+6ZKyM7Vj8f12C3sze37NCYuVqPjYtQsPERT2zRWsI+vPtmTIEnakpalHlGRal8lXMNr11CYn6/2ZGZLkh7fsFNj69VUneCgstnhU/TtyK76OyTbaoaK7Nk932hrVrztGAAAAAAAnDWKuB4mzZmlyTs+Yhxc4H92Z+YoMSdPvatXcU2L8PdThyrhWn6k+OFGcgsKtCYlXb1r/P8yPg6HeteoohX/G1KhZUSoVienKzk3X6uT05VdUKD6IcFaejhFa1PSdX2DWmW5WycV36eLXq3PcCpnI9vk6Z7tHyi7INd2FAAAAAAAzgpFXA/zzO6vFZ+XbDsG4DES/zf8QY3AALfpNQIDdDAnr9hlDufmyWmkGoH+7ssE+LvW1z+6qkbUrqELFq7ShNWbNb1dE4X4+ei+tdv0TJvGen/nAfWav0IXL1qtTWkZZbBnJSuoU0e39+XjuTRsy47X1D3f2I4BAAAAAMBZ8bMdAP/vl+S1+u7wctsxAK8xqVk9TWpWz/X8+c271bt6Ffn7OPTi1j36pU9H/Zx4WLev2qyfencon1ABAXrmskZK9c0sn+15gS8PLVHfyFbqW6WV7SgAAAAAAJwRunp5iMN56dxNHShG9P964B7Mcb8k/mBObpGetsdUC/CXr0NFeuoezM1zre94W9Iz9eXeRN3brJ7+SEpRt2qRqh7or4trVtfalAyl5+eXwt6c3JJLeurXKhRwS9tjuz5TSn759qgGAAAAAKC0UMT1EI/v/lxH8tNtxwA8Tt2QQEUH+mvhoWTXtLS8fP2VnKbOVSOKXSbAx0dtI8PclikwRgsPJatT1fAi7Y0xumfNVj3SqoFC/XzlNEZ5BUfHpc4zRpLkNKW3TyVJ69hWD7fic6AsHMpP05TdX9mOAQAAAADAGaGI6wG+T1quX5LX2o4BWJOR79TfKen6O+VoAXN3Zrb+TknX3qxsORwO3dCgtl7cukdz4pO0ITVDt6/arJigAA2OjXKtY+TitXpnx37X85sa1tbHu+P12Z4EbU7L1L1rtynT6dTouJgi2/94d4KiAvx1fszR9XWpGqFFSSlacSRVM7bvU9OwEEX6l/HoM9WiNGFQ0QIzSs+PR/7ST0dW2Y4BAAAAAMBpY0xcyxJyk/X0nq9txwCsWp2cphFL/nY9f2T9DknSqDrRmt6+qW5tVFuZTqfuXrtVqXn56lItQjO7tFaQ7/9/D7UzM1uHc/9/+IRLatVQUk6epm7erYM5uWoVEaqZXVoXuUHawZxcTd+6R9/1bOua1qFquP7ZsLauWrZeUYH+eqld07La9aN8fPT25e20NzCtbLcDPbX7K50T3lhV/cJsRwEAAAAA4JQ5jDHlcJEwSvLPLW9qceom2zGA0/bqkgA1/nGR7RiVwuYhvXR7lxzbMbzGBVU76OmGV9mOAXiF1NRURUZGKiUlRRERxQ+BUx7arbjL2rYBYHWnabYjAAA81OkcLzOcgkXfJy2ngAt4udymTTSxU+7JG6LUzD7yl+Yn/33yhgAAAAAAeAiKuJak5mfq+b3f2o4BwKaQUN1/SU3l+nJBRHl7cvcXSnNm2Y4BAAAAAMApoYhryYv7vtfhfO5CD3iz70Z11dpQCok2HMxL1Wv7f7QdAwAAAACAU0IR14JV6Tv01aGltmMAsCihdxe9Up8bmdn0n8RF2pi5z3YMAAAAAABOiiJuOcs3Tj2x+wsZcfk04K0KatfWbX35+LXNqQJN2f2luL8nAAAAAMDTUUUoZx8lLNCWrAO2YwCwxd9fUy9rqlQ/p+0kkLQ6Y6e+TuLKCAAAAACAZ6OIW4725xzW6wd+sh0DgEVLh/XU/KqMh+1JXto3Syn5GbZjAAAAAABQIoq45eiZPV8ruyDXdgwAlqS3b6OHWlEs9DRH8jM0fd8s2zEAAAAAACgRRdxy8kfqJv2ass52DAC2VIvSnReE206BEnx9aKnWZuyyHQMAAAAAgGJRxC0HTlOg5/b813YMALb4+OidUe20OzDPdhKUoEBGT+3+ipucAQAAAAA8EkXccvDlocXalh1vOwYAS7YO6qH/xKbZjoGTWJe5R7OPrLQdAwAAAACAIijilrE0Z5Ze2/+j7RgALMlr0lh3dmYs7Iri5X2zlVeQbzsGAAAAAABuKOKWsbcPzNMR7noOeKeQUN0/rJZyfblEv6LYn3tYnxxcaDsGAAAAAABuKOKWoQO5RzQz8TfbMQBYMmtkV60JzbIdA6fp3wd+Vmo+PzcAAAAAgOegiFuGXtk3WzmGy3IBb5TY6xy91IBxcCuiFGem3o7/2XYMAAAAAABcKOKWkY2Z+zTr8ArbMQBYUFCzlm7v52s7Bs7CJ4m/60DuEdsxAAAAAACQRBG3zLy+/0cZMQ4m4HX8/PXcyKZK9nPaToKzkGPy9cq+2bZjAAAAAAAgiSJumVifsUe/pqyzHQOABcsv6al51biZYWXww+EV2py533YMAAAAAAAo4paF1w/MsR0BgAUZ7VrrgdYUcCuLAhnNODDXdgwAAAAAACjilra/M3brt5T1tmMAKG9VqmrCkEgZh+0gKE0/J6/R1qx42zEAAAAAAF6OIm4pe2M/vXABr+Nw6P3LO2h3YK7tJChlRkZv0RsXAAAAAGAZRdxStDZjl35P3WA7BoBytu38HppZM812DJSRn46s0s7sRNsxAAAAAABejCJuKaIXLuB98ho10p1d82zHQBkqkNFbB362HQMAAAAA4MUo4paSNRm7tDB1o+0YAMpTSIgeGF5bOT7GdhKUsdmHV2pPziHbMQAAAAAAXooibil558A82xEAlLMfL+umVWFZtmOgHDhVQG9cAAAAAIA1FHFLwa7sg1qQss52DADl6FDPznqhIePgepNZScu1L+ew7RgAAAAAAC9EEbcUfJS4QAXicmrAWxTUrKnb+/nbjoFylq8CzUz8zXYMAAAAAIAXooh7lpLzM/TtoT9txwBQXvz8NW1kcx32z7edBBZ8c2iZMpzZtmMAAAAAALwMRdyz9NnBP5RtuDM94C1WXNxDc6ul244BS9ILsvXNoWW2YwAAAAAAvAxF3LOQW5CvTxMX2o4BoJxktm2lf7XJtB0Dln1y8HcVmALbMQAAAAAAXoQi7lmYdXiFkvK5sRHgFapU1Z1Dqsg4bAeBbXtykrQgZb3tGAAAAAAAL0IR9yx8mLDAdgQA5cHh0IeXd9DOoFzbSeAhPuLzHwAAAABQjijinqHFqZu0LTvedgwA5WDHed31UU163eP/LU/fpk2Z+2zHAAAAAAB4CYq4Z+iLg4ttRwBQDvIbNtQd3fJtx4AH+ijxN9sRAAAAAABegiLuGUjKS9OvKetsxwBQ1oJD9NDwOsrxMbaTwAP9ePgvpeRn2I4BAAAAAPACFHHPwH+TlinfOG3HAFDGfhrRTSvCs2zHgIfKNfn6/vAK2zEAAAAAAF6AIu5pMsbo60NLbccAUMYOde+k5xszDi5O7L+HltmOAAAAAADwAhRxT9OytK3anXPIdgwAZcjE1tT4c/1tx0AFsClrv9Zn7LEdAwAAAABQyVHEPU1fHuKGZkCl5uenF0Y2V5I/Q6bg1HydxNUZAAAAAICyRRH3NBzJT9cvyWttxwBQhv66qKfmRKXbjoEKZPbhv5RTkGc7BgAAAACgEqOIexq+TVquPG5oBlRamW1aaXLbTNsxUMGkObP085E1tmMAAAAAACoxirin4fukP21HAFBWIqvorqFVZRy2g6AiYkgFAAAAAEBZooh7irZlxWtz1gHbMQCUBYdDH4/qqB1BObaToIJanrZNe3OSbMcAAAAAAFRSFHFP0ezDf9mOAKCM7BzYXR/UTrMdAxWYkdHswyttxwAAAAAAVFIUcU/RnCMUcYHKKL9hA03olm87BiqBn46ssh0BAAAAAFBJUcQ9Besy9mh3ziHbMQCUtqBgPTy8rrJ8je0kqAQ2Zx3QzuxE2zEAAAAAAJUQRdxTwCWyQOX084huWh6eaTsGKpE59MYFAAAAAJQBirgnUWAKOCkHKqGk7p30bJN02zFQyfx0eJXtCAAAAACASogi7kmsTN+uxLwU2zEAlCITE6Pbz/W3HQOV0NbseG3PSrAdAwAAAABQyVDEPQl64QKVjJ+fpo9sqSR/p+0kqKS4wRkAAAAAoLRRxD2J+cl/244AoBSturCnZldnGAWUHb78AwAAAACUNoq4J7Ahc68O5qXajgGglGS1bqn72nEjM5St7dkJ2pYVbzsGAAAAAKASoYh7AguS19mOAKC0RERq4tCqMg7bQeANfk9ZbzsCAAAAAKASoYh7Ar9xEg5UDg6HZl7eSduCc20ngZf4PWWD7QgAAAAAgEqEIm4JDuWlan3mXtsxAJSCXQO66/3aabZjwIusytipdGe27RgAAAAAgEqCIm4JfktZLyNjOwaAs5Rfv77u6J5vOwa8TL5xaknqZtsxAAAAAACVBEXcEjCUAlAJBAXpkUvrKsuXL2RQ/hYypAIAAAAAoJRQxC1GbkE+PaiASmDeiO76MyLLdgx4qYWpFHEBAAAAAKWDIm4xlqdvU1YBN0ACKrLD3TpqapN02zHgxQ7mpWpT5j7bMYAT2rdvn6688kpFRUUpODhYbdq00fLly13zjTF66KGHVLNmTQUHB2vgwIHasmWLxcQAAACAd6KIW4xlqZycABWZiY7R+HMDbccAGFIBHu3IkSPq2bOn/P39NXv2bK1fv17PP/+8qlat6mozdepUvfTSS3rjjTe0dOlShYaGatCgQcrO5sZ9AAAAQHnysx3AEy1P32o7AoAz5eurly9vqYMB9MKFfYtSN+q6mgNtxwCK9cwzzyguLk7vvvuua1qDBg1c/zfG6MUXX9QDDzygSy65RJL0wQcfKCYmRt98841Gjx5d7pkBAAAAb0VP3ONkOnO0IWOv7RgAztCai3pqVhQFXHiGtRm7lc3wPPBQ3377rTp37qyRI0cqOjpaHTp00FtvveWav2PHDsXHx2vgwP//IiIyMlJdu3bV4sWLi11nTk6OUlNT3R4AAAAAzh5F3OOsTN+ufBXYjgHgDGS1aqH72nIjM3iOXJOvNRm7bMcAirV9+3a9/vrratKkiebMmaObb75Z48eP1/vvvy9Jio+PlyTFxMS4LRcTE+Oad7ynnnpKkZGRrkdcXFzZ7gQAAADgJSjiHufPNIZSACqkiAjdfWGUnHyqwcMsT9tmOwJQrIKCAnXs2FFTpkxRhw4ddOONN+qGG27QG2+8ccbrnDx5slJSUlyPPXv2lGJiAAAAwHtR7jgOJ9tAxfTpqHO0JTjHdgygiBX8XYGHqlmzplq2bOk2rUWLFtq9e7ckKTY2VpKUkJDg1iYhIcE173iBgYGKiIhwewAAAAA4exRxC8lwZmtjJuPhAhXNngHd9W4dxl2EZ1qbsUt5Bfm2YwBF9OzZU5s2bXKbtnnzZtWrV0/S0ZucxcbGat68ea75qampWrp0qbp3716uWQEAAABvRxG3kL/SdzAeLlDB5Nevr/E9+L2F58ox+VrPF4TwQHfeeaeWLFmiKVOmaOvWrZo5c6ZmzJihW2+9VZLkcDg0YcIEPfHEE/r222+1du1ajR07VrVq1dKwYcPshgcAAAC8jJ/tAJ5kRTqXvAIVSlCQHru0njJ9M20nAU5oVfoOtQurbzsG4Oacc87R119/rcmTJ+uxxx5TgwYN9OKLL2rMmDGuNvfcc48yMjJ04403Kjk5Wb169dKPP/6ooKAgi8kBAAAA70MRt5C/M7j5BlCRzL+0u5ZGpNuOAZzUX+k7NE79bccAirjwwgt14YUXljjf4XDoscce02OPPVaOqQAAAAAcj+EU/scYow1c7gpUGEe6dtTTTSngomJYlbHTdgQAAAAAQAVGEfd/duccUpozy3YMAKfAREfrjoGBtmMAp+xIfrric4/YjgEAAAAAqKAo4v7PuszdtiMAOBW+vnp1ZGsl+OfbTgKclo2Z+2xHAAAAAABUUBRx/2ddBkMpABXB2qE99V2NNNsxgNPGkD0AAAAAgDNFEfd/1mdyUzPA02W3bK572zPsCSomeuICAAAAAM4URVxJBaaAk2vA04VHaNJF1eXkUwsV1Ab+zgAAAAAAzhDlEEk7shOVWZBjOwaAE/hs1DnaEszvKSquhLxkHclPtx0DAAAAAFABUcSVtJ5xCgGPtufcbno7LtV2DOCs0RsXAAAAAHAmKOJK2pp1wHYEACVw1q2r8T2N7RhAqdjIl4YAAAAAgDNAEVfStuwE2xEAFCcwSE9c1kCZvgW2kwClgvHXAQAAAABngiKupO1Z8bYjACjGgku764+ITNsxgFKzMzvRdgRUEMnJyfr3v/+tyZMn6/Dhw5KklStXat8+vggAAAAAvJGf7QC2ZRXkan/uEdsxABwnuUsHTWnGTaBQuezOOSRjjBwOh+0o8GBr1qzRwIEDFRkZqZ07d+qGG25QtWrV9NVXX2n37t364IMPbEcEAAAAUM68vifuzuxEGTHeJuBJTI1ojR8YbDsGUOqyCnKVmJdiOwY83F133aWrr75aW7ZsUVBQkGv6kCFD9Ntvv1lMBgAAAMAWirjZB21HAFCYj69eH9VKCQF5tpMAZYK/OziZP//8UzfddFOR6bVr11Z8PENAAQAAAN7I64u4uxifEPAo64b21H9rMIwCKq9dORRxcWKBgYFKTU0tMn3z5s2qUaOGhUQAAAAAbKOIy8k04DFyWjTT3R2ybMcAytQueuLiJC6++GI99thjyss7ekWCw+HQ7t27de+992rEiBGW0wEAAACwgSIuJ9OAZwiP0N0X1ZDT6z+VUNnx5SFO5vnnn1d6erqio6OVlZWlvn37qnHjxgoPD9eTTz5pOx4AAAAAC/xsB7Btf+4R2xEASPpyZGdtCkmzHQMoc3x5iJOJjIzU3LlztXDhQq1Zs0bp6enq2LGjBg4caDsaAAAAAEu8uoibU5CnI/mMvQnYtq9/N82oSwEX3mF/TpLyjVN+Dl/bUeDhevXqpV69etmOAQAAAMADeHURNyE32XYEwOs569bV+J7Gdgyg3OSrQEl5aYoJqGI7CjzUSy+9VOx0h8OhoKAgNW7cWH369JGvL18EAAAAAN7Cq4u4ByjiAnYFBOqJEQ2U7pdpOwlQrhLyUijiokQvvPCCDh48qMzMTFWtWlWSdOTIEYWEhCgsLEyJiYlq2LCh5s+fr7i4OMtpAQAAAJQHr76FUEJesu0IgFf7/dIe+iOSAi68z8HcFNsR4MGmTJmic845R1u2bFFSUpKSkpK0efNmde3aVdOnT9fu3bsVGxurO++803ZUAAAAAOXEq3vixtMTF7Am5Zz2eqI5Y1LDOyXmUcRFyR544AF9+eWXatSokWta48aN9dxzz2nEiBHavn27pk6dqhEjRlhMCQAAAKA8eXVP3PjcI7YjAF7JVK+hO84Lth0DsOZgXqrtCPBgBw4cUH5+fpHp+fn5io+PlyTVqlVLaWncEBIAAADwFl5dxOXGZoAFPr564/LWOhBQtEABeItEhlPACfTv31833XST/vrrL9e0v/76SzfffLPOPfdcSdLatWvVoEEDWxEBAAAAlDOvLuIynAJQ/tYP6aFvajCMArwbPXFxIm+//baqVaumTp06KTAwUIGBgercubOqVaumt99+W5IUFham559/3nJSAAAAAOXFq8fEPZxPIQkoTznNm+mejjm2YwDWMSYuTiQ2NlZz587Vxo0btXnzZklSs2bN1KxZM1eb/v3724oHAAAAwAKvLuKmOrNsRwC8R1i47rsoWnk+2baTANYdpIiLU9C8eXM1b97cdgwAAAAAHsBri7iZzhzlG6ftGIDX+GrUOVofyk14AElKc2Yr3zjl5/C1HQUeau/evfr222+1e/du5ebmus2bNm2apVQAAAAAbPHaIm6qM9N2BMBr7O/XVW/WpYALFJbhzFakX6jtGPBA8+bN08UXX6yGDRtq48aNat26tXbu3CljjDp27Gg7HgAAAAALvPbGZin5FHGB8uCMi9PtvWynADxPmpOhRVC8yZMna9KkSVq7dq2CgoL05Zdfas+ePerbt69GjhxpOx4AAAAAC7y3iEtPXKDsBQRqyoiGSvcrsJ0E8DhpjMuOEmzYsEFjx46VJPn5+SkrK0thYWF67LHH9Mwzz1hOBwAAAMAGry3ipuZz8gyUtUXDe2hhFb4wAYqTTk9clCA0NNQ1Dm7NmjW1bds217xDhw7ZigUAAADAIsbEBVAmUju102Mt0m3HADwWRVyUpFu3blq4cKFatGihIUOGaOLEiVq7dq2++uordevWzXY8AAAAABZ4bxGXMXGBslO9usYPCpGUbzsJ4LHSGU4BJZg2bZrS049+Cfboo48qPT1d//nPf9SkSRNNmzbNcjoAAAAANnhtETezINd2BKBy8vHVjJFtdCCAXrjAiXBjM5SkYcOGrv+HhobqjTfesJgGAAAAgCfw2jFxcwvoIQiUhY0X9NCXMRRwgZNhOAWUpGHDhkpKSioyPTk52a3ACwAAAMB7eG0RN89QxAVKW06zpprUKcd2DKBCyCrgdwXF27lzp5xOZ5HpOTk52rdvn4VEAAAAAGzz2uEU8kzRkyMAZyEsTJMviVGeD70LgVPhNAW2I8DDfPvtt67/z5kzR5GRka7nTqdT8+bNU/369S0kAwAAAGCbFxdx6YkLlKb/juyidSFptmMAFYbTGNsR4GGGDRsmSXI4HBo3bpzbPH9/f9WvX1/PP/+8hWQAAAAAbPPeIm4BPXGB0nKgbxe9Vo8CLnA6nFwRguMUFBztnd2gQQP9+eefql69uuVEAAAAADyF1xZxc+mJC5SKgjp1NL63QxK9CoHT4RTDKaB4O3bssB0BAAAAgIfx2iIuY+ICpSAgQE9f1lipfhm2kwAVDmPi4kTmzZunefPmKTEx0dVD95h33nnHUioAAAAAtlDEBXDGFg/rqQVV0m3HACqkfIq4KMGjjz6qxx57TJ07d1bNmjXlcDhsRwIAAABgmdcWcQs4eQbOyk8NA/XfaMbBBc4Uf4dQkjfeeEPvvfeerrrqKttRAAAAAHgIH9sBbPF1eO2uA6WCAi5wdhgTFyXJzc1Vjx49bMcAAAAA4EG8tpLp5/C1HQEA4MWM4WaAKN7111+vmTNn2o4BAAAAwIN47XAKFHEBADb5+3jtn2CcRHZ2tmbMmKGff/5Zbdu2lb+/v9v8adOmWUoGAAAAwBavPYP0YzgFAIBFAQ6v/ROMk1izZo3at28vSfr777/d5nGTMwAAAMA7ee0ZJD1xAQA2BdATFyWYP3++7QgAAAAAPIzXdkf1p4gLALCInrg4ma1bt2rOnDnKysqSxDjKAAAAgDfz2iIuPXEBADbRExclSUpK0oABA9S0aVMNGTJEBw4ckCRdd911mjhxouV0AAAAAGzw2iIuPXEBADYFOvxP3ghe6c4775S/v792796tkJAQ1/TLL79cP/74o8VkAAAAAGzx2m5A9MQFANjkT09clOCnn37SnDlzVKdOHbfpTZo00a5duyylAgAAAGCT1/bEDfMNsh0BAODFAhkTFyXIyMhw64F7zOHDhxUYGGghEQAAAADbvLaIG+Fb9OQIAIDywpi4KEnv3r31wQcfuJ47HA4VFBRo6tSp6t+/v8VkAAAAAGzx2jPIcD964gIA7Inky0SUYOrUqRowYICWL1+u3Nxc3XPPPVq3bp0OHz6sRYsW2Y4HAAAAwAKv7Ykb7htsOwIAwItF+lHERfFat26tzZs3q1evXrrkkkuUkZGhSy+9VH/99ZcaNWpkOx4AAAAAC7y2Jy7DKQAAbIr0C7UdAR4sMjJS//rXv2zHAAAAAOAh6IkLAIAFVSjiogTvvvuuPv/88yLTP//8c73//vsWEgEAAACwzXuLuH4UcQEA9lThihCU4KmnnlL16tWLTI+OjtaUKVMsJAIAAABgm/cWcX25sRkAwI4Qn0D5+3jtiEY4id27d6tBgwZFpterV0+7d++2kAgAAACAbV5bxA3yCVCggxNoAED5q8JNzXAC0dHRWrNmTZHpq1evVlRUlIVEAAAAAGzz2iKuJEX5R9iOAADwQtzUDCdyxRVXaPz48Zo/f76cTqecTqd++eUX3XHHHRo9erTteAAAAAAs8OquqDEBkdqfe9h2DACAl4lkPFycwOOPP66dO3dqwIAB8vM7eqhWUFCgsWPHMiYuAAAA4KW8uogb7R9pOwIAwAvV4EoQlMAYo/j4eL333nt64okntGrVKgUHB6tNmzaqV6+e7XgAAAAALPHqIm5MQBXbEQAAXoi/PyiJMUaNGzfWunXr1KRJEzVp0sR2JAAAAAAewKvHxI2hJy4AwAKKuCiJj4+PmjRpoqSkJNtRAAAAAHgQ7y7ichINALCALxFxIk8//bTuvvtu/f3337ajAAAAAPAQXj2cAmPiAgBsiOVLRJzA2LFjlZmZqXbt2ikgIEDBwcFu8w8f5qasAAAAgLfx6iJuTABFXABA+asVWM12BHiwF1980XYEAAAAAB7Gq4u4Nfwj5SsfOVVgOwoAwEuE+wYr3Df45A3htcaNG2c7AgAAAAAP49Vj4vo6fLikFQBQrmoH0AsXJ7dt2zY98MADuuKKK5SYmChJmj17ttatW2c5GQAAAAAbvLqIK0n1g6JtRwAAeBGGUsDJLFiwQG3atNHSpUv11VdfKT09XZK0evVqPfzww5bTAQAAALCBIm5QDdsRAABepG5gddsR4OHuu+8+PfHEE5o7d64CAgJc088991wtWbLEYjIAAAAAtlDEpScuAKAcNQqOtR0BHm7t2rUaPnx4kenR0dE6dOiQhUQAAAAAbKOISxEXAFCOGgbF2I4AD1elShUdOHCgyPS//vpLtWvXtpAIAAAAgG0UcQMp4gIAyodDDoq4OKnRo0fr3nvvVXx8vBwOhwoKCrRo0SJNmjRJY8eOtR0PAAAAgAVeX8SNDohUqE+g7RgAAC8Q4x+pEF/+5uDEpkyZohYtWqhu3bpKT09Xy5Yt1adPH/Xo0UMPPPCA7XgAAAAALPCzHcAT1AuqofWZe23HAABUcg2D6YWLkhUUFOjZZ5/Vt99+q9zcXF111VUaMWKE0tPT1aFDBzVp0sR2RAAAAACWUMTV0XFxKeICAMpawyBuaoaSPfnkk3rkkUc0cOBABQcHa+bMmTLG6J133rEdDQAAAIBlXj+cgiQ15qQaAFAOGtETFyfwwQcf6LXXXtOcOXP0zTff6LvvvtPHH3+sgoIC29EAAAAAWEYRV1LzEO70DAAoe9zUDCeye/duDRkyxPV84MCBcjgc2r9/v8VUAAAAADwBRVxJzUPq2I4AAKjkfORQ4+CatmPAg+Xn5ysoKMhtmr+/v/Ly8iwlAgAAAOApGBNXUpR/uGr4R+hgXqrtKACASqp+ULTCfINO3hBeyxijq6++WoGBga5p2dnZ+uc//6nQ0FDXtK+++spGPAAAAAAWUcT9n+bBtSniAgDKTKuQONsR4OHGjRtXZNqVV15pIQkAAAAAT0MR939ahsbp99QNtmMAACqp1qF1bUeAh3v33XdtRwAAAADgoRgT93/oIQUAKEsUcQEAAAAAZ4oi7v9wcg0AKCv+Dl81C65lOwYAAAAAoIKiiPs/Uf7hivWvYjsGAKASahpcS/4+jGAEAAAAADgzFHELaRtWz3YEAEAlxNUeAAAAAICzQRG3kE5hjWxHAABUQhRxAQAAAABngyJuIZ3CKeICAEpf21Cu9AAAAAAAnDmKuIU0DopVFd9Q2zEAAJVItH+k6gdF244BAAAAAKjAKOIW4nA41CG8ge0YAIBK5JzwxrYjAKfk6aeflsPh0IQJE1zTsrOzdeuttyoqKkphYWEaMWKEEhIS7IUEAAAAvBRF3ON0ZlxcAEAp6hLexHYE4KT+/PNPvfnmm2rbtq3b9DvvvFPfffedPv/8cy1YsED79+/XpZdeaiklAAAA4L0o4h6HcXEBAKWpCz1x4eHS09M1ZswYvfXWW6pataprekpKit5++21NmzZN5557rjp16qR3331Xf/zxh5YsWVLsunJycpSamur2AAAAAHD2KOIep1lwLYX7BtmOAQCoBGoHVFOtwGq2YwAndOutt2ro0KEaOHCg2/QVK1YoLy/PbXrz5s1Vt25dLV68uNh1PfXUU4qMjHQ94uLiyjQ7AAAA4C0o4h7Hx+Gj9mENbccAAFQCjIcLT/fpp59q5cqVeuqpp4rMi4+PV0BAgKpUqeI2PSYmRvHx8cWub/LkyUpJSXE99uzZUxaxAQAAAK/jZzuAJ+oW3kS/p6y3HQMAUMExHi482Z49e3THHXdo7ty5CgoqnauQAgMDFRgYWCrrAgAAAPD/6IlbjN6RLW1HAABUAvTEhSdbsWKFEhMT1bFjR/n5+cnPz08LFizQSy+9JD8/P8XExCg3N1fJycluyyUkJCg2NtZOaAAAAMBLUcQtRr2gGqobWMN2DABABdY4KFbRAZG2YwAlGjBggNauXatVq1a5Hp07d9aYMWNc//f399e8efNcy2zatEm7d+9W9+7dLSYHAAAAvA/DKZSgT2QLfZR40HYMAEAF1bdKK9sRgBMKDw9X69at3aaFhoYqKirKNf26667TXXfdpWrVqikiIkK33367unfvrm7dutmIDAAAAHgteuKWgCEVAABno18kRVxUfC+88IIuvPBCjRgxQn369FFsbKy++uor27EAAAAAr0NP3BJ0CmuoEJ9AZRbk2I4CAKhgovzC1Sa0nu0YwGn79ddf3Z4HBQXp1Vdf1auvvmonEAAAAABJ9MQtkb+Pn7pFNLUdAwBQAfWObCGHw2E7BgAAAACgkqCIewK9I1vYjgAAqID6VWl98kYAAAAAAJwiirgn0DuypRyiJxUA4NQFOriSAwAAAABQuijinkAN/wi1Ca1rOwYAoALpEtFUwT4BtmMAAAAAACoRirgnMahqe9sRAAAVSL/IVrYjAAAAAAAqGYq4J3F+1fbyYUgFAMAp8JOPzq3SxnYMAAAAAEAlQxH3JKIDItUhrIHtGACACqBrRFNV8w+zHQMAAAAAUMlQxD0Fg6p2sB0BAFABXFCto+0IAAAAAIBKiCLuKRhYta18eakAACcQ5PDXAIZSAAAAAACUASqTpyDKP1znhDe2HQMA4MH6VGmlEN9A2zEAAAAAAJUQRdxTNKhae9sRAAAebAhDKQAAAAAAyghF3FM0oEob+Tl8bccAAHigCN9g9YpobjsGAAAAAKCSooh7iiL9QtUvspXtGAAAD3Re1Xby9/GzHQMAAAAAUElRxD0Nl1bvajsCAMADMZQCAAAAAKAsUcQ9Dd0jmqlWQFXbMQAAHqReYA115uaXAAAAAIAyRBH3NPg4fHRJVBfbMQAAHmREje62IwAAAAAAKjmKuKdpWPUu8pHDdgwAgAcIcPjpkqhzbMcAAAAAAFRyFHFPU2xAVXWPaGY7BgDAAwys2lZV/EJtxwAAAAAAVHIUcc8ANzgDAEjSZdUZSgEAAAAAUPYo4p6BvlVaq5pfmO0YAACLGgbFqFN4I9sxAAAAAABegCLuGfB3+Go4vXEBwKuNqN7NdgQAAAAAgJegiHuGLq/RU34OX9sxAAAWBDr8dDE3NAMAAAAAlBOKuGcoJqCKzq/aznYMAIAFg6t1VIRfiO0YAAAAAAAvQRH3LFwV3dd2BABAOXPIobEx/WzHAAAAAAB4EYq4Z6FlaJw6hjW0HQMAUI56RTRX4+BY2zEAAAAAAF6EIu5ZuiqG3rgA4E3GxvazHQEAAAAA4GUo4p6lfpGtFBcYZTsGAKActAypoy7hTWzHAAAAAAB4GYq4Z8nH4aN/RPexHQMAUA7GxvS3HQEAAAAA4IUo4paCYVFdFO4bbDsGAKAM1QqopvOrtrMdAwAAAADghSjiloIQ30CNie5tOwYAoAxdGd1Hvg7+bAIAAAAAyh9no6VkTHRfhfsG2Y4BACgDkb4hGl69q+0YAAAAAAAvRRG3lET4BesKeuMCQKV0ZUxfhfgG2o4BAAAAAPBSFHFL0VXRfRXmQ29cAKhMIn1D9A++pAMAAAAAWEQRtxRF+IXQGxcAKpkrY/oqjOFyAAAAAAAWUcQtZVfF9FGoD5fcAkBlQC9cAAAAAIAnoIhbyiL9QumNCwCVxLjY/vTCBQAAAABYRxG3DIyN6UtvXACo4KL8wumFCwAAAADwCBRxy0CkX6jGxvSzHQMAcBauix2gYJ8A2zEAAAAAAKCIW1bGxvRTDf8I2zEAAGcg1r+KRtboYTsGAAAAAACSKOKWmRDfQN1Sa7DtGACAM3Bb7QsU4ONnOwYAAAAAAJIo4papS6K6qHFQrO0YAIDT0CokThdW62w7BgAAAAAALhRxy5Cvw0d31rnIdgwAwGmYFHeJHA6H7RgAAAAAALhQxC1jvSJbqFt4U9sxAACn4Pyq7dQxrKHtGAAAAAAAuKGIWw7uqnORfESvLgDwZAEOP91R+0LbMQAAAAAAKIIibjloFlJbF0YxviIAeLIx0X1UJzDKdgwAAAAAAIqgiFtObq81RKE+gbZjAACKUc0vTNfXHGg7BgAAAAAAxaKIW06iAyJ1U83zbccAABTj1loXKMw3yHYMAAAAAACKRRG3HI2J6aNGQbG2YwAACmkVEqdLq3e1HQMAAAAAgBJRxC1Hfg5f3V/3UtsxAAD/4ycfPVRvlHwc/DkEAAAAAHguzlrLWefwxrqwGjc5AwBP8I+YPmoeUtt2DAAAAAAATogirgUT61ysSN8Q2zEAwKvVCqiqm2sOsh0DAAAAAICToohrQTX/ME2oc6HtGADg1SbXHaEQ30DbMQAAAAAAOCmKuJYMj+qqjmENbccAAK90XpV26hPZ0nYMAAAAAABOCUVcSxwOhx6uN0pBDn/bUQDAq4T7BuneusNtxwAAAAAA4JT52Q7gzeoHReu22kP03N7/2o6CUuTMyFXCG38oZf5W5R/JVHCzaNWa2E8hrWIlScYYJby5WIe/Xitneo5C29VS7fsGKLBu1ROu99Bnq3TwwxXKT8pQUJMaqn13f4W0jnXN3z9tgY58v04+wf6Kva2Xql7QwjUv+efNOjJrvRq8MKxM9hmoSMbXHqoa/hG2YwAAAAAAcMroiWvZmOjeDKtQyex9Yq7Slu5S3GOD1fTTsQrrWk/bb/lSeYnpkqSD7y/XoU9XqfbkgWr83hXyCfLXjtu/UkFOfonrTP5pkw688JtibuimJh+NUXDT6tpx+1fKP5wpSUr9bZuS52xUg1cuVeztvbX3ibnKT86SJDnTcxT/2iLVvvfcst95wMN1C2+qkdV72I4BAAAAAMBpoYhrmY/DR4/Xv0LBPgG2o6AUFGTnK+WXLao5vrfCOtZRYFwVxd7UXYFxVZT0xWoZY3Tok5WKua6LIvs1UnCTGop7bLDyDmYo9ddtJa734McrVW1Ya1W7uJWCGkap9uSBcgT56fC3f0uSsnccVmjHOgppGauqg5vLNzRQuftSJEkHpv+uqBHtFBBLz0N4t3DfYD1Wf7QcDoftKAAAAAAAnBaKuB6gTmCU7qx9ke0YKAXGWSA5jRwB7iOVOAL9lLFqv3L3pSg/KVNhXeq65vmGBSqkdawy1u4vdp0FeU5lbUxQWNf/X8bh41B4l7rKXHNAkhTctIayNiQoPzVbmRsSVJCTr4C4KspYtU9ZmxJVfXT70t9ZoIK5L264YgKq2I4BAAAAAMBpY0xcDzGqRg/NS16jpWlbbEfBWfANDVBI25pK/PdSBTWoJr9qIUqes0mZaw8ooE4V5ScdHf7ALyrEbTm/aiGuecdzJmdJTiO/akWXyd55RJIU3r2+qlzQQlvHzpRPoJ/iHhkkn2B/7Xtqnuo8MkhJX6zRof+skl+VINX510AFNapeBnsPeK7zqrTThVGdbccAAAAAAOCM0BPXQzgcDj1af7TCfIJsR8FZintssCSjDRe8pbU9XtKhT/9SlUHN5PAp20u4Y2/qrubfXKum/xmryP6NdfDdZQrrUlcOPx8lvrNUjd8epWrD2mjPw3PKNAfgaar7heuBepfZjgEAAAAAwBmjiOtBagZU1aS4S2zHwFkKrFNFjWaMUuvfb1OLWderyQf/kMkvUEDtSFcP3ON73eYfzizSO/cY3yrBkq/DdROzwsv4l7BM9s7DOjJ7o2Ju7qGMFXsV2qG2/KqGqMp5TZW1MVHOjNxS2FOgYnik/uWq4hdqOwYAAAAAAGeMIq6HGV69qwZVbW87BkqBT7C//KuHKT81W2mLdymib0NXITf9zz2uds70HGX+Ha/QNrWKX4+/r4Kbxyh92f8vYwqM0v/co5C2NYu0N8Zo35SfVevOPvINCZBxGpn8gqPz8p1HGxUUlOKeAp5rRPVu6h3Z0nYMAAAAAADOCmPieqCH6o3S+sw92pOTZDsKzkDa4p2SkQLrVVXOnmQdeOl3BdWvqmoXt5LD4VD1Kzoq8e2lCoirooDakUp4/Q/51whVRL9GrnVsv/kLRfRrrOqXt5ck1RjTUXsemaPgltEKaRWrQzP/UkFWnqpe1KrI9g9/87f8qgQros/R9YW2q6WEGYuVsfaA0v7YocCG1eQbzrAdqPzqBdbQpDpc3QAAAAAAqPgo4nqgMN8gTW0wVmM3vaQ847QdB6fJmZ6j+FcWKS8xXb4RgYo8t4lib+0ph5+vJKnGuM4qyM7Tvik/y5mWo9D2tdTgpUvlE/j/v445e1OUn5zlel7l/GbKP5KlhDcWKz8pU0FNa6jBy8PlH+V+iXheUoYS31mmxu9c7poW0jpWNa7spJ0TvpFf1RDFPTKojF8BwL4gh7+eazhOIb6BtqMAAAAAAHDWHMYYYzsEivdxwm+auvcb2zEAoMJ5uN4oXVq9m+0YgNdLTU1VZGSkUlJSFBERYS1HuxV3Wds2AKzuNM12BACAhzqd42XGxPVgY2L6qH9ka9sxAKBCuTjqHAq4AAAAAIBKhSKuh3us/mjVDKhqOwYAVAiNgmJ1f90RtmMAAAAAAFCqKOJ6uAi/ED3d4Er58aMCgBMK9gnQcw3HKdgnwHYUAAAAAABKFZXBCqB9WAPdWeci2zEAwKM9WHekGgbH2I4BAAAAAECpo4hbQVwZ01cXR51jOwYAeKQR1btpaFQn2zEAAAAAACgTFHErkAfrjlTrkLq2YwCAR+kQ1kCT4y61HQMAAAAAgDJDEbcCCfDx0wuNrlEN/wjbUQDAI9QKqKppDa+Rv4+f7SgAAAAAAJQZirgVTHRApKY1vFoBDgoWALxbiE+gpje6TtX8w2xHAQAAAACgTFHErYDahtXXA3Uvsx0DAKxxyKEpDcaoaUgt21EAAAAAAChzFHErqEuqd9E/onvbjgEAVtxe6wL1r9LadgwAAAAAAMoFRdwKbGKdi9UzorntGABQroZU66jrag60HQMAAAAAgHJDEbcC83P46rmG49QypI7tKABQLtqE1tUj9S63HQMAAAAAgHJFEbeCC/EN1CuNb1DtgGq2owBAmaobWEMvNbpegT7+tqMAAAAAAFCuKOJWAlH+4XqtyY2q6hdqOwoAlIka/hF6o8lNquYfZjsKAAAAAADljiJuJVE/KFovNbpOQQ56qAGoXMJ9g/Rq4xtUO5ArDgAAAAAA3okibiXSNqy+nml4lXz5sQKoJAIcfnqx0bVqFlLbdhQAAAAAAKyh2lfJ9KvSWpPrXmo7BgCcNR859FSDK9U5vLHtKAAAAAAAWEURtxIaWaOHbqk12HYMADgr99cdoYFV29qOAQAAAACAdRRxK6mbap6v62MH2o4BAGfk5pqDNLJGD9sxAAAAAADwCBRxK7Hbaw/R2Jh+tmMAwGm5Pnag/llrkO0YAAAAAAB4DIq4ldzEOhdrdI1etmMAwCm5JuZc3V57iO0YAAAAAAB4FIq4XuC+uOEaUb2b7RgAcEJXx/TXhDoX2o4BAAAAAIDHoYjrBRwOhx6sO1IXVetsOwoAFOuq6L66s85FtmMAAAAAAOCRKOJ6CYfDocfqj9YFVTvYjgIAbq6M7qNJcZfYjgEAAAAAgMeiiOtFfBw+erLBGF0S1cV2FACQJP0jurfujhtmOwYAAAAAAB6NIq6X8XX46NF6l2tMdG/bUQB4uSuj++jeuOG2YwAAAAAA4PH8bAdA+XM4HLonbrhCfYM048Bc23EAeKGbaw7SP2sNsh0DAAAAAIAKgSKuF7u11gUK8wnStH3f2Y4CwEs45NDdcZdoTHQf21EAAAAAAKgwGE7By42L7a8H646Ujxy2owCo5Pzko8fqj6aAC3iIp556Suecc47Cw8MVHR2tYcOGadOmTW5tsrOzdeuttyoqKkphYWEaMWKEEhISLCUGAAAAvBdFXOiyGt01pcGV8uPtAKCMBDn8Na3RNbo46hzbUQD8z4IFC3TrrbdqyZIlmjt3rvLy8nT++ecrIyPD1ebOO+/Ud999p88//1wLFizQ/v37demll1pMDQAAAHgnhzHG2A4Bz7AoZYPu3v6BMgpybEcBUIlE+AbrpcbXq0NYA9tRAJzAwYMHFR0drQULFqhPnz5KSUlRjRo1NHPmTF122WWSpI0bN6pFixZavHixunXrdtJ1pqamKjIyUikpKYqIiCjrXShRuxV3Wds2AKzuNM12BACAhzqd42W6XsKlZ2QLvdvsNsX4V7EdBUAlEetfRe80u40CLlABpKSkSJKqVasmSVqxYoXy8vI0cOBAV5vmzZurbt26Wrx4cbHryMnJUWpqqtsDAAAAwNmjiAs3zUJq68Pm49UsuLbtKAAquFYhcfq4xQQ1Ca5pOwqAkygoKNCECRPUs2dPtW7dWpIUHx+vgIAAValSxa1tTEyM4uPj/6+9+w6PqkzYP37PpBcmnRSYgKlAAkiQorTQu4i4oLAIRFEkaCDYscGyWCmCKOhPFpC6asR9RRCkLW+WpQVQWghBiosCEUjoCcl5/1Dn5ywgCIEzkO/nunKROc9znrnnXHogN4dzLrrOq6++qoCAAMeX3W6/3tEBAACACoESFxcI9wzU9MQhahZQy+woAG5S7YLqalpiukI9zPvn0wCuXHp6urZu3ap58+Zd0zrPPfecCgsLHV8HDhwop4QAAABAxUaJi4vydfPS27Fp6hXWxOwoAG4yAyPa6I3bHpS31dPsKACuwJAhQ/TFF19oxYoVqlq1qmN7RESEiouLdfz4caf5hw4dUkRExEXX8vLyks1mc/oCAAAAcO0ocXFJbharno/uoeFV75ZVFrPjAHBxHhY3ja7eW0OqdJLFwjkDcHWGYWjIkCH67LPPtHz5ct12m/O9q+vXry8PDw8tW7bMsS03N1f79+/XnXfeeaPjAgAAABWau9kB4PoeDE9VtFeYRnw3WyfLzpodB4ALCnL307jYAUrxjzE7CoArlJ6erjlz5ujzzz9XpUqVHPe5DQgIkI+PjwICAvTQQw8pMzNTwcHBstlsevzxx3XnnXeqcePGJqcHAAAAKhauxMUVSQ1M0qyaGaruVdnsKABcTJx3hGbVGEqBC9xk3nvvPRUWFio1NVWRkZGOr/nz5zvmjB8/Xl26dFGPHj3UvHlzRUREKCsry8TUAAAAQMVkMQzDMDsEbh4nS89qxHeztbJwm9lRALiAzsH19WK1P8mH+98CuIiioiIFBASosLDQ1Pvj1t2Yadp7A8CW+uPMjgAAcFF/5M/LXImLP8TfzVsTYtOUHtWR++QCFZiHxU0jontozG19KHABAAAAALjOKHHxh1ksFj0S2VaT4x9RoJuf2XEA3GCRnkGanvi4eoY1MTsKAAAAAAAVAiUurtpdtkTNq5WpJF+72VEA3CB32RI1r2amkv2izY4CAAAAAECFQYmLaxLpGaQZiY+rX3iqLNxeAbhlWWXRoMh2mhw3UIHuXIEPAAAAAMCNRImLa+ZhdVdm1bs1Jf5RhXmY99ASANdHZY8AvRf/qB6L6iCrhd82AAAAAAC40fhpHOWmsS1BH9d6Ui0CksyOAqCctA2sq09qPanGtgSzowAAAAAAUGFR4qJcBbn7a2LcQ3rOfq+8LO5mxwFwlfysXvpL9Qf0Vmw/BXD7BAAAAAAATEWJi+vi/spNNadmpuJ9Is2OAuAPut3vNv291pO6O6SB2VEAAAAAAIAocXEdxflEaHaNoepbuYWsPPQMcHnusio9qqOmJaarqleI2XEAAAAAAMAvKHFxXXlZPfSkvZtm1HhCMd7hZscBcAmx3hGaUeMJPRLZVm48vAwAAAAAAJfCT+q4Ier4VdPfaw7XwIi2cuc/O8BleFjc9Fhke82vmalkv2iz4wAAAAAAgIvgyVO4YTys7hpSpaPaBtXRy/vma8fp782OBFRot/vdpper9VSMD1fJAwAAAADgyrgkEjdcom8VzaqRoSeiOsnTwt8jADeav9Vbz9t7aHriEApcAAAAAABuAjRoMIW7xU0PRbZRq6DaGrM/S+tO5JkdCagQUgOS9Hx0D4V7BpodBQAAAAAAXCFKXJjqNu9wfZDwmL46ulnjvv+Hfiw5bnYk4JYU4RGoJ+3d1DaortlRAAAAAADAH0SJC5fQPvh2NQuoqQ9/XKaZh1aq2DhvdiTgluBt8VC/iJYaENFKPlZPs+MAAAAAAICrQIkLl+Hr5qXHq3RSt5AGeuPAAq0u2mF2JOCm1i7odmVW7apIzyCzowAAAAAAgGtAiQuXE+0dpnfiB2rV8W168/sFOnDuJ7MjATeVRJ8qesZ+j+pXijU7CgAAAAAAKAeUuHBZLQKTdKctUfOPZOv//fC1jpeeMjsS4NKC3P00JKqT7g1tJKvFanYcAAAAAABQTihx4dI8re7qG95C3UMbafqPKzTr8CqdKSs2OxbgUnytXupTubn6RaSqkpuP2XEAAAAAAEA5o8TFTcHfzVtDqnTU/ZWb6P0flurTgn/rvFFqdizAVF4Wd/0prIkeimitYA9/s+MAAAAAAIDrhBIXN5VQD5uej+6hvuEtNPk/i7T42GYZMsyOBdxQ7rKqW2hDPRrZTuGegWbHAQAAAAAA1xklLm5Kdq9QvRbTV/1Ot9TUg0u0snAbZS5ueVZZ1CG4nh6LbK9o7zCz4wAAAAAAgBuEEhc3tZq+VTUhLk15Zw7qwx+WacmxLSpVmdmxgHLlJqvaBtXVw5GtFe8TZXYcAAAAAABwg1Hi4pYQ7xOl12L6avDZDvrboeX6n582qIR75uIm52VxV7fQhuoX3lJVvULMjgMAAAAAAExCiYtbSrR3mF6u1kuDIttrxqEV+rRgrc6WFZsdC/hDKrn5qFdYE/Wu3EwhHpXMjgMAAAAAAExGiYtbUrhnoJ62d9fDEW0198hqfXJkjY6eP2l2LOB3hXnY1LdyC90Xdqf83LzNjgMAAAAAAFwEJS5uacEe/kqP6qiBEW311bHNmnt4tbadPmB2LMBJLd+quj+sqToFp8jDymkZAAAAAAA4s5odALgRPK3u6hpyh+bUHKaPamT8XJZZ3MyOhQrMw+KmTsEp+ijxCc2tmaluoQ0pcAEAAADcsv75z3+qU6dOCgsLk8VikcVi0ZQpU5zmlJSUaOTIkYqJiZGnp6eqVq2qYcOG6eTJy//L2hMnTmjYsGGqWrWqPD09FRsbq5EjR+r8+fOOOQsXLlRSUpJ8fX3VoEEDrV271mmNwYMHKzk5WSUlJeXzoYFyRGOACqeOXzXVua2anqzaTR8f+Zc+KVijIyVFZsdCBVHFM1g9QhvrntBG3O8WAAAAQIWRk5OjpUuXKiYmRgUFBRedk5aWplmzZslqtSo+Pl579uzRhAkTtGnTJi1fvlxW68WvRSwrK1PXrl21atUqeXh4KCYmRnl5eXrllVeUn5+vmTNn6vjx4+rVq5caN26s1atX66677lKPHj30/fffS5Kys7P1wQcfaPXq1fLw8LhuxwG4WlyJiworxKOSBkW116LaL2pCbJpaBibLnatzcR24y6qWAcmaHDdQXyQ/r4ci21DgAgAAAKhQ+vbtq6KiIn311VcXHc/JydGsWbMkSW+//bZ27typTz/9VJK0atUqLViw4JJrL1iwQKtWrZIkZWVlaefOnZowYYIk6aOPPlJOTo7y8vJ06tQpNWrUSMHBwUpJSdF//vMfFRQUqLi4WAMHDtRjjz2mxo0bl9+HBsoRJS4qPA+Lm1oGJmtCbJq+rvOynrbfoxo+VcyOhVtAkq9dz9i7a2mdVzQhLk1NA2rKauG0CwAAAKDiCQkJkY+PzyXHFy1a5Pi+R48ekqTOnTvL2/vnhz4vXrz4svv6+PioU6dOTmv8um9cXJz8/Py0du1aHT16VDk5OapSpYpCQ0P117/+VadOndKYMWOu/gMC1xltAvAbQe7+6lO5uebXGq6Paz6pP1duoWB3f7Nj4SYS5RmkgRFt9HnSs5pTc5h6V26mYA/+GwIAAACA33PgwP9/CHnlypUlSVarVaGhoZKk/fv3X3bfkJAQxy0XwsPDHeP79+9XUFCQ5s+fr4MHD6pq1ary9/fXJ598ou3bt+u1117Te++9p8mTJys6OlqRkZEaPny40/10AbNxT1zgEhJ8o/SUbzcNq9pF2YU7teTYFq0q3KoTpWfNjgYXU8nNW22D6qpL8B1K8Y+RxWIxOxIAAAAA3BIMwyi3/Tp37qzOnTs7XpeVlalZs2bq0aOHLBaLnn32WQ0ePFhVqlTRiBEjFB8fr0GDBl11dqA8UeICl+FucVOLwCS1CExSSdl5ZRflaumxLVpVuE0nSs+YHQ8mCXWvpBaBSWodWFsNK8XLw8rpFAAAAACult1ud3x/+PBhRUZGqqysTD/99JMkKTo6+rL7FhQUqKysTFarVYcPH3aMX2rfd999V7m5uVqwYIFee+01SdKgQYNUvXp1jRgxQkuXLqXEhcvgdgrAH+BhdVdqYJL+eltvrag7SlPiH9WfQu9SmIfN7Gi4AaK9wtQ/vKVmJj6hr+u8opeq9VSTgJoUuAAAAABwjTp06OD4/tcHmi1cuFBnz551Gl+3bp1q1KihGjVqaN26dU5jZ8+e1Zdffum0xn+v/avvv/9ezz//vMaNG6ewsDDHlbuenp7y8PAo748HXDOLcbXXpQNwMAxDW0/v17+KcvWvwlxtPbVP51VmdixcIzdZVcuvqloEJKlVYG3F+kSYHQkAbipFRUUKCAhQYWGhbDbz/sKz7sZM094bALbUH2d2BMAlZGVl6emnn9b58+e1b98+SVJYWJhsNpsaNWqk2bNnq3fv3po7d66sVqsSEhKUn5+vkpISNWvWTCtXrpTVatXKlSvVsmVLSdKKFSuUmpqq0tJSpaam6n//93/l4eGh2NhY7dq1S2VlZerdu7dmz559QZ67775b586d01dffSVJ+vLLL9W5c2eNHz9edrtd9913nyZPnqzBgwffuIOECueP/HmZy8eAcmCxWFTbr5pq+1XTo5HtdKL0jNYV5f1c6hbl6mDxUbMj4gpV8wpTY1uCGlWKV4NK8bK5X/rpqQAAAACAK1NUVKT8/HynbUeOHNGRI0dUtWpVSdKMGTMUHx+vmTNnKj8/X2FhYbrvvvs0evRoxwPLLsbNzU0LFy7Uiy++qE8++UT5+fmKjo7Wgw8+qBdeeOGC+X//+9+1bNkybd261bGtU6dOGj16tF5//XWVlJToiSee0COPPFJOnx64dlyJC9wAe88e1pqiXK09kafNJ/fq2PmTZkfCL4Ld/X8pbRPU2BavCM8gsyMBwC2DK3EBgCtxAQCXxpW4gIup7l1Z1b0r64HKzSRJ+84e0eaT32nLqb3adPI7fXf2sAzx9ynXm1UWxXiHq65/ddX2q6Y6ftUU4x0ui8VidjQAAAAAAIBLosQFTFDNO0zVvMPULbShJKnw/CltObVPm09+p29P7deu0wd1vPSUySlvfkHu/qrtF606vxS2yX7R8nPzNjsWAAAAAADAH0KJC7iAAHc/NQ+opeYBtRzbDhUfV96ZH5R75qB2nT6oXWcOat/ZIyrlgWkX8LC4qbp3ZcX7RCrOO0JxPpGK94lUlFew2dEAAAAAAACuGSUu4KLCPQMV7hmopgE1HdvOlhUr/8wh7T7zgw6cK9CBcz/98muBikrPmJj2xqjk5qMqnsGq4hWsGO8Ixfn8XNhW9w6Tu8XN7HgAAAAAAADXBSUucBPxtnoqyc+uJD/7BWNF509r/2+K3f+c+0kFJUUqKDmhn86f0NGSky59Fa9VFgW4+yrI3V9VPIMV5RWsKl4hjtK2imeIbO4+ZscEAAAAAAC44ShxgVuEzd1Xye7RSvaLvuh4mVGmY+dP6aeSEyo4f0I/lRTpp5ITOll6TmfKzulU6TmdKjunM7/8err0nE7/8ut5o0yGDJXJkGTIMKSft/zyODbDkMVikZfFQ15WD3lZ3eVpcZePm6d8rV7ytXrJx81T/m7eCnL3V7C7v4Lc/RXk4adg90oKcvdToLuf3CzWG3nIAAAAAAAAbgqUuEAFYbVYFeJRSSEelZRgdhgAAAAAkPRj12ZmRwBQwUX8z2qzI1wRLnsDAAAAAAAAABdGiQsAAAAAAAAALowSFwAAAAAAAABcGCUubloWi0ULFiwo93VTU1M1dOjQcl8XAAAAAAAAuBqUuLgq/fv3l8Vi0aBBgy4YS09Pl8ViUf/+/a9orb1798pisWjz5s3lGxIAAAAAAAC4BVDi4qrZ7XbNmzdPZ86ccWw7e/as5syZo+joaBOTAQAAAAAAALcOSlxctZSUFNntdmVlZTm2ZWVlKTo6WvXq1XNsW7x4sZo2barAwECFhISoS5cuys/Pd4zfdtttkqR69erJYrEoNTXVMTZt2jQlJSXJy8tLkZGRGjJkiFOGgoICde/eXb6+voqPj9c//vEPp/GtW7eqY8eO8vf3V3h4uPr27auCggLH+KlTp/Tggw/K399fkZGRGjt2bLkcGwAAAAAAAKC8UOLimqSlpelvf/ub4/W0adM0YMAApzmnTp1SZmamNmzYoGXLlslqtap79+4qKyuTJK1bt06S9PXXX+uHH35wlMLvvfee0tPT9cgjj+jbb7/VP/7xD8XFxTmtPXLkSPXs2VPffPONOnXqpD59+ujo0aOSpOPHj6tVq1aqV6+eNmzYoMWLF+vQoUPq2bOnY/+nnnpKq1at0ueff64lS5Zo5cqVysnJKf8DBQAAAAAAAFwld7MD4Ob25z//Wc8995z27dsnScrOzta8efO0cuVKx5wePXo47TNt2jSFhYVp+/btSk5OVlhYmCQpJCREERERjnmjR4/W8OHDlZGR4djWoEEDp7X69++vBx54QJI0ZswYTZw4UevWrVOHDh30zjvvqF69ehozZozTe9vtdu3atUtRUVH68MMPNWvWLLVu3VqSNGPGDFWtWrUcjgwAAAAAAABQPihxcU3CwsLUuXNnTZ8+XYZhqHPnzgoNDXWak5eXp5deeklr165VQUGB4wrc/fv3Kzk5+aLrHj58WAcPHnSUq5dSp04dx/d+fn6y2Ww6fPiwJGnLli1asWKF/P39L9gvPz9fZ86cUXFxsRo1auTYHhwcrMTExCv78AAAAAAAAMANQImLa5aWlua4V+3kyZMvGO/atauqVaumDz74QFFRUSorK1NycrKKi4svuaaPj88VvbeHh4fTa4vF4iiJT548qa5du+r111+/YL/IyEjt3r37it4DAAAAAAAAMBP3xMU169Chg4qLi1VSUqL27ds7jf3000/Kzc3VCy+8oNatW6tmzZo6duyY0xxPT09JUmlpqWNbpUqVVL16dS1btuyqc6WkpGjbtm2qXr264uLinL78/PwUGxsrDw8PrV271rHPsWPHtGvXrqt+TwAAAAAAAKC8UeLimrm5uWnHjh3avn273NzcnMaCgoIUEhKi999/X7t379by5cuVmZnpNKdy5cry8fFxPHissLBQkvTKK69o7NixmjhxovLy8pSTk6NJkyZdca709HQdPXpUDzzwgNavX6/8/Hx99dVXGjBggEpLS+Xv76+HHnpITz31lJYvX66tW7eqf//+slr53wIAAAAAAACug7YK5cJms8lms12w3Wq1at68edq4caOSk5M1bNgwvfnmm05z3N3dNXHiRE2dOlVRUVHq1q2bJKlfv36aMGGC3n33XSUlJalLly7Ky8u74kxRUVHKzs5WaWmp2rVrp9q1a2vo0KEKDAx0FLVvvvmmmjVrpq5du6pNmzZq2rSp6tevfw1HAgAAAAAAAChfFsMwDLNDAAAA4NZTVFSkgIAAFRYWXvQve2+UuhszLz8JAK6TLfXHmR3Bpf3YtZnZEQBUcBH/s9q09/4jf17mSlwAAAAAAAAAcGGUuAAAAAAAAADgwihxAQAAAAAAAMCFUeICAAAAAAAAgAujxAUAAAAAAAAAF0aJCwAAAAAAAAAujBIXAAAAAAAAAFwYJS4AAAAAAAAAuDBKXAAAAAAAAABwYZS4AAAAAAAAAODCKHEBAAAAAAAAwIVR4gIAAAAAAACAC6PEBQAAAAAAAAAXRokLAAAAAAAAAC6MEhcAAAAAAAAAXBglLgAAAAAAAAC4MEpcAAAAAAAAAHBhlLgAAAAAAAAA4MIocQEAAAAAAADAhVHiAgAAAAAAAIALo8QFAAAAAAAAABdGiQsAAAAAAAAALowSFwAAAAAAAABcGCUuAAAAAAAAALgwSlwAAAAAAAAAcGGUuAAAAAAAAADgwihxAQAAAAAAAMCFUeICAAAAAAAAgAujxAUAAAAAAAAAF0aJCwAAAAAAAAAujBIXAAAAAAAAAFwYJS4AAAAAAAAAuDBKXAAAAAAAAABwYZS4AAAAuKTJkyerevXq8vb2VqNGjbRu3TqzIwEAAAAVDiUuAAAALmr+/PnKzMzUyy+/rJycHNWtW1ft27fX4cOHzY4GAAAAVCjuZgcAAACAaxo3bpwGDhyoAQMGSJKmTJmihQsXatq0aXr22WcvmH/u3DmdO3fO8bqwsFCSVFRUdGMCX0LpyXOXnwQA14nZ50BXd6LkvNkRAFRwviaep3/9PcIwjMvOpcQFAADABYqLi7Vx40Y999xzjm1Wq1Vt2rTRmjVrLrrPq6++qpEjR16w3W63X7ecAODqAvSu2REAAL8nIMDsBDpx4oQCLpODEhcAAAAXKCgoUGlpqcLDw522h4eHa+fOnRfd57nnnlNmZqbjdVlZmY4ePaqQkBBZLJbrmhe4HoqKimS323XgwAHZbDaz4wAA/gvnadzsDMPQiRMnFBUVddm5lLgAAAAoF15eXvLy8nLaFhgYaE4YoBzZbDbKAQBwYZyncTO73BW4v+LBZgAAALhAaGio3NzcdOjQIafthw4dUkREhEmpAAAAgIqJEhcAAAAX8PT0VP369bVs2TLHtrKyMi1btkx33nmnickAAACAiofbKQAAAOCiMjMz1a9fP91xxx1q2LChJkyYoFOnTmnAgAFmRwNuCC8vL7388ssX3CYEAOAaOE+jIrEYhmGYHQIAAACu6Z133tGbb76pH3/8UbfffrsmTpyoRo0amR0LAAAAqFAocQEAAAAAAADAhXFPXAAAAAAAAABwYZS4AAAAAAAAAODCKHEBAAAAAAAAwIVR4gIAAAAAAOC6S01N1dChQ695nf79++uee+655nWAmwklLgAAAADT/Pjjj3r88ccVExMjLy8v2e12de3aVcuWLTM7mqmWLl2qhIQE2Ww29e3bV8XFxY6xwsJCJSQkaN++fSYmBICfy1SLxaJBgwZdMJaeni6LxaL+/fs7tmVlZekvf/nLNb/v22+/renTp1/zOpcze/Zs2e12BQUFKTMz02ls7969SkhIUFFR0XXPAUiUuAAAAABMsnfvXtWvX1/Lly/Xm2++qW+//VaLFy9Wy5YtlZ6ebmq235amN1pZWZl69+6tQYMGac2aNdqwYYPef/99x/izzz6rQYMGqVq1aqZlBIBf2e12zZs3T2fOnHFsO3v2rObMmaPo6GinucHBwapUqdI1v2dAQIACAwOveZ3fU1BQoIcfflhvvfWWlixZolmzZumLL75wjA8ePFivvfaabDbbdc0B/IoSFwAAAIApBg8eLIvFonXr1qlHjx5KSEhQUlKSMjMz9e9//9sxb//+/erWrZv8/f1ls9nUs2dPHTp0SJK0a9cuWSwW7dy502nt8ePHKzY21vF669at6tixo/z9/RUeHq6+ffuqoKDAMZ6amqohQ4Zo6NChCg0NVfv27SVJ48aNU+3ateXn5ye73a7Bgwfr5MmTTu/1wQcfyG63y9fXV927d9e4ceMuKBc+//xzpaSkyNvbWzExMRo5cqTOnz9/0eNSUFCggoICDR48WElJSbr77ru1Y8cOSdK//vUvrV+/XhkZGX/waAPA9ZGSkiK73a6srCzHtqysLEVHR6tevXpOc//7dgrvvvuu4uPj5e3trfDwcN13332OsU8++US1a9eWj4+PQkJC1KZNG506dUrShbdTSE1N1RNPPKGnn35awcHBioiI0CuvvOL03jt37lTTpk3l7e2tWrVq6euvv5bFYtGCBQsu+rn27NmjgIAA9erVSw0aNFDLli0d5+K5c+fKw8ND995771UcMeDqUOICAAAAuOGOHj2qxYsXKz09XX5+fheM/1qClpWVqVu3bjp69KhWrVqlpUuXas+ePerVq5ckKSEhQXfccYdmz57ttP/s2bPVu3dvSdLx48fVqlUr1atXTxs2bNDixYt16NAh9ezZ02mfGTNmyNPTU9nZ2ZoyZYokyWq1auLEidq2bZtmzJih5cuX6+mnn3bsk52drUGDBikjI0ObN29W27Zt9de//tVp3dWrV+vBBx9URkaGtm/frqlTp2r69OkXzPtVWFiYIiMjtWTJEp0+fVqrV69WnTp1VFJSoscee0xTp06Vm5vbHzjaAHB9paWl6W9/+5vj9bRp0zRgwIDf3WfDhg164oknNGrUKOXm5mrx4sVq3ry5JOmHH37QAw88oLS0NO3YsUMrV67UvffeK8MwLrnejBkz5Ofnp7Vr1+qNN97QqFGjtHTpUklSaWmp7rnnHvn6+mrt2rV6//33NWLEiN/NFx8fr9OnT2vTpk06evSo1q9frzp16ujYsWN68cUX9c4771zp4QHKhwEAAAAAN9jatWsNSUZWVtbvzluyZInh5uZm7N+/37Ft27ZthiRj3bp1hmEYxvjx443Y2FjHeG5uriHJ2LFjh2EYhvGXv/zFaNeundO6Bw4cMCQZubm5hmEYRosWLYx69epdNvfHH39shISEOF736tXL6Ny5s9OcPn36GAEBAY7XrVu3NsaMGeM056OPPjIiIyMv+T6rV6827rjjDqN69erG4MGDjeLiYmPUqFFGRkaGsXXrVuOuu+4yEhISjEmTJl02MwBcL/369TO6detmHD582PDy8jL27t1r7N271/D29jaOHDlidOvWzejXr59jfosWLYyMjAzDMAzj008/NWw2m1FUVHTBuhs3bjQkGXv37v3d9/3tuk2bNnWa06BBA+OZZ54xDMMwFi1aZLi7uxs//PCDY3zp0qWGJOOzzz675OfLysoykpOTjdjYWOPll182DMMw0tLSjPHjxxurVq0ybr/9diMpKcn4+OOPf+coAeXD3cwCGQAAAEDFZPzO1VS/tWPHDtntdtntdse2WrVqKTAwUDt27FCDBg10//3368knn9S///1vNW7cWLNnz1ZKSopq1KghSdqyZYtWrFghf3//C9bPz89XQkKCJKl+/foXjH/99dd69dVXtXPnThUVFen8+fM6e/asTp8+LV9fX+Xm5qp79+5O+zRs2NDpvolbtmxRdna205W3paWlTuv8t6ZNm2r9+vWO17t27dLMmTO1adMmNW/eXBkZGerYsaOSk5PVvHlz1alT54qOJwBcD2FhYercubOmT58uwzDUuXNnhYaG/u4+bdu2VbVq1RQTE6MOHTqoQ4cO6t69u3x9fVW3bl21bt1atWvXVvv27dWuXTvdd999CgoKuuR6/30ejIyM1OHDhyVJubm5stvtioiIcIw3bNjwsp+re/fuTuf4VatW6ZtvvtGkSZMUFxenuXPnKiIiQg0bNlTz5s1VuXLly64JXC1upwAAAADghouPj7/ovWyvRkREhFq1aqU5c+ZIkubMmaM+ffo4xk+ePKmuXbtq8+bNTl95eXmOf7or6YLbOuzdu1ddunRRnTp19Omnn2rjxo2aPHmypD/24LOTJ09q5MiRTu/97bffKi8vT97e3le0xqOPPqqxY8eqrKxMmzZt0p/+9CdVrlxZLVq00KpVq644CwBcL2lpaZo+fbpmzJihtLS0y86vVKmScnJyNHfuXEVGRuqll15S3bp1dfz4cbm5uWnp0qVatGiRatWqpUmTJikxMVHffffdJdfz8PBwem2xWFRWVnbNn+tX586d0+DBgzV16lTt3r1b58+fV4sWLZSYmKiEhAStXbu23N4LuBhKXAAAAAA3XHBwsNq3b6/Jkyc7HlTzW8ePH5ck1axZUwcOHNCBAwccY9u3b9fx48dVq1Ytx7Y+ffpo/vz5WrNmjfbs2aP777/fMZaSkqJt27apevXqiouLc/q62P14f7Vx40aVlZVp7Nixaty4sRISEnTw4EGnOYmJiU5XzEq64HVKSopyc3MveO+4uDhZrZf/kezDDz9UcHCw7r77bpWWlkqSSkpKHL/+ug0AzNShQwcVFxerpKTE8XDIy3F3d1ebNm30xhtv6JtvvtHevXu1fPlyST+XsE2aNNHIkSO1adMmeXp66rPPPruqbImJiTpw4IDjoZjShefqyxk9erQ6dOiglJQUlZaWOj2cknMxbgRKXAAAAACmmDx5skpLS9WwYUN9+umnysvL044dOzRx4kTdeeedkqQ2bdqodu3a6tOnj3JycrRu3To9+OCDatGihe644w7HWvfee69OnDihxx57TC1btlRUVJRjLD09XUePHtUDDzyg9evXKz8/X1999ZUGDBjwuz90x8XFqaSkRJMmTdKePXv00UcfOR549qvHH39cX375pcaNG6e8vDxNnTpVixYtksViccx56aWXNHPmTI0cOVLbtm3Tjh07NG/ePL3wwguXPUaHDx/W6NGjNWnSJElSUFCQatasqQkTJmjNmjVatmyZmjRpcmUHHACuIzc3N+3YsUPbt2+/oocvfvHFF5o4caI2b96sffv2aebMmSorK1NiYqLWrl2rMWPGaMOGDdq/f7+ysrJ05MgR1axZ86qytW3bVrGxserXr5+++eYbZWdnO87Bvz1fX8r27ds1f/58jRo1SpJUo0YNWa1Wffjhh1q4cKF27typBg0aXFU24EpR4gIAAAAwRUxMjHJyctSyZUsNHz5cycnJatu2rZYtW6b33ntP0s8/XH/++ecKCgpS8+bN1aZNG8XExGj+/PlOa1WqVEldu3bVli1bnG6lIElRUVHKzs5WaWmp2rVrp9q1a2vo0KEKDAz83Sth69atq3Hjxun1119XcnKyZs+erVdffdVpTpMmTTRlyhSNGzdOdevW1eLFizVs2DCn2yS0b99eX3zxhZYsWaIGDRqocePGGj9+vKpVq3bZY5SRkaHhw4c7ldLTp0/XvHnz1KVLFz311FMUBwBchs1mk81mu6K5gYGBysrKUqtWrVSzZk1NmTJFc+fOVVJSkmw2m/75z3+qU6dOSkhI0AsvvKCxY8eqY8eOV5XLzc1NCxYs0MmTJ9WgQQM9/PDDGjFihCRd9rY2hmHokUce0bhx4xz/esPHx0fTp0/XqFGj9NBDD+mdd95RlSpVriobcKUsxpU+UQAAAAAAcFkDBw7Uzp07tXr1arOjAAAuITs7W02bNtXu3bsVGxtrdhzgstzNDgAAAAAAN7O33npLbdu2lZ+fnxYtWqQZM2bo3XffNTsWAOA3PvvsM/n7+ys+Pl67d+9WRkaGmjRpQoGLmwYlLgAAAABcg3Xr1umNN97QiRMnFBMTo4kTJ+rhhx82OxYA4DdOnDihZ555Rvv371doaKjatGmjsWPHmh0LuGLcTgEAAAAAAAAAXBgPNgMAAAAAAAAAF0aJCwAAAAAAAAAujBIXAAAAAAAAAFwYJS4AAAAAAAAAuDBKXAAAAAAAAABwYZS4AAAAAAAAAODCKHEBAAAAAAAAwIVR4gIAAAAAAACAC/s/imb6GbjAWfkAAAAASUVORK5CYII=\n"}, "metadata": {}}], "source": ["def calculate_observation_coverage(matches: List[Dict], total_gt: int) -> Dict:\n", "    \"\"\"Calculate observation coverage metrics\"\"\"\n", "    coverage_score = len(matches) / total_gt if total_gt > 0 else 0\n", "\n", "    coverage_analysis = {\n", "        'total_gt_observations': total_gt,\n", "        'matched_observations': len(matches),\n", "        'coverage_percentage': coverage_score * 100,\n", "        'unmatched_observations': total_gt - len(matches)\n", "    }\n", "\n", "    return coverage_analysis\n", "\n", "# Calculate coverage\n", "coverage_results = calculate_observation_coverage(observation_matches, len(gt_observations))\n", "\n", "print(\"📊 OBSERVATION COVERAGE ANALYSIS\")\n", "print(\"=\" * 40)\n", "print(f\"Total Ground Truth Observations: {coverage_results['total_gt_observations']}\")\n", "print(f\"Successfully Matched: {coverage_results['matched_observations']}\")\n", "print(f\"Coverage Percentage: {coverage_results['coverage_percentage']:.1f}%\")\n", "print(f\"Unmatched Observations: {coverage_results['unmatched_observations']}\")\n", "\n", "# Visualize coverage\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Coverage pie chart\n", "labels = ['Matched', 'Unmatched']\n", "sizes = [coverage_results['matched_observations'], coverage_results['unmatched_observations']]\n", "colors = ['#2ecc71', '#e74c3c']\n", "ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)\n", "ax1.set_title('Observation Coverage', fontsize=14, fontweight='bold')\n", "\n", "# Coverage bar chart\n", "categories = ['Coverage %', 'Missing %']\n", "values = [coverage_results['coverage_percentage'], 100 - coverage_results['coverage_percentage']]\n", "bars = ax2.bar(categories, values, color=colors)\n", "ax2.set_ylabel('Percentage')\n", "ax2.set_title('Coverage vs Missing Observations', fontsize=14, fontweight='bold')\n", "ax2.set_ylim(0, 100)\n", "\n", "# Add value labels on bars\n", "for bar, value in zip(bars, values):\n", "    height = bar.get_height()\n", "    ax2.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "             f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "I7p1KRpp3b8C"}, "source": ["## Step 3: Heuristic Match Accuracy Analysis\n", "\n", "For each matched observation, analyze how well the model identified the violated heuristics using Gemini."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 503}, "id": "0mZ9_QIK3b8C", "executionInfo": {"status": "ok", "timestamp": 1749815376022, "user_tz": -330, "elapsed": 176106, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "95756d44-a4bd-4561-bb45-b3d0dd1d8b67"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🧠 Analyzing heuristic accuracy using Gemini AI...\n", "\n", "🔍 Analyzing match 1/9...\n", "✅ Analysis complete - Overlap: 35%\n", "\n", "🔍 Analyzing match 2/9...\n", "✅ Analysis complete - Overlap: 33.33%\n", "\n", "🔍 Analyzing match 3/9...\n", "✅ Analysis complete - Overlap: 67%\n", "\n", "🔍 Analyzing match 4/9...\n", "✅ Analysis complete - Overlap: 66.67%\n", "\n", "🔍 Analyzing match 5/9...\n", "✅ Analysis complete - Overlap: 33%\n", "\n", "🔍 Analyzing match 6/9...\n", "✅ Analysis complete - Overlap: 67%\n", "\n", "🔍 Analyzing match 7/9...\n", "✅ Analysis complete - Overlap: 80%\n", "\n", "🔍 Analyzing match 8/9...\n", "✅ Analysis complete - Overlap: 80%\n", "\n", "🔍 Analyzing match 9/9...\n", "✅ Analysis complete - Overlap: 33%\n"]}], "source": ["def analyze_heuristic_accuracy_with_gemini(matches: List[Dict]) -> Dict:\n", "    \"\"\"Analyze heuristic accuracy using Gemini for semantic comparison\"\"\"\n", "    print(\"🧠 Analyzing heuristic accuracy using Gemini AI...\")\n", "\n", "    heuristic_results = []\n", "\n", "    for i, match in enumerate(matches):\n", "        gt_heuristics = match['gt_item']['heuristics_violated']\n", "        model_heuristics = match['model_item']['heuristics_violated']\n", "\n", "        print(f\"\\n🔍 Analyzing match {i+1}/{len(matches)}...\")\n", "\n", "        # Use Gemini to compare heuristic lists\n", "        heuristic_prompt = f\"\"\"\n", "        Compare these two lists of UX heuristics violated for the same issue.\n", "        Analyze semantic similarity and provide detailed comparison.\n", "\n", "        Ground Truth Heuristics: {gt_heuristics}\n", "        Model Response Heuristics: {model_heuristics}\n", "\n", "        Context - Issue Location: {match['gt_item']['location']}\n", "        Context - Issue Description: {match['gt_item']['observation'][:200]}...\n", "\n", "        Provide analysis in JSON format:\n", "        {{\n", "            \"semantic_overlap_score\": <0-100>,\n", "            \"correctly_identified\": [\"list of semantically matching heuristics\"],\n", "            \"missed_heuristics\": [\"list of GT heuristics not captured by model\"],\n", "            \"extra_heuristics\": [\"list of model heuristics not in GT\"],\n", "            \"precision_score\": <0-100>,\n", "            \"recall_score\": <0-100>,\n", "            \"analysis\": \"detailed analysis of the comparison\"\n", "        }}\n", "        \"\"\"\n", "\n", "        try:\n", "            response = model.generate_content(heuristic_prompt)\n", "            json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "            if json_match:\n", "                heuristic_analysis = json.loads(json_match.group())\n", "                heuristic_analysis['match_index'] = i\n", "                heuristic_analysis['gt_location'] = match['gt_item']['location']\n", "                heuristic_results.append(heuristic_analysis)\n", "                print(f\"✅ Analysis complete - Overlap: {heuristic_analysis['semantic_overlap_score']}%\")\n", "            else:\n", "                print(\"❌ Failed to parse Gemini response\")\n", "        except Exception as e:\n", "            print(f\"❌ Error in heuristic analysis: {e}\")\n", "\n", "    return heuristic_results\n", "\n", "# Analyze heuristic accuracy\n", "heuristic_analysis = analyze_heuristic_accuracy_with_gemini(observation_matches)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 954}, "id": "XkEKB8dD3b8C", "executionInfo": {"status": "ok", "timestamp": 1749815376692, "user_tz": -330, "elapsed": 579, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "756a3409-ca8e-4805-adcb-ad4e34741b0e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "🧠 HEURISTIC ACCURACY ANALYSIS\n", "========================================\n", "Average Precision: 55.6%\n", "Average Recall: 59.2%\n", "Average Semantic Overlap: 55.0%\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1600x1200 with 4 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["# Display heuristic analysis results\n", "if heuristic_analysis:\n", "    print(\"\\n🧠 HEURISTIC ACCURACY ANALYSIS\")\n", "    print(\"=\" * 40)\n", "\n", "    # Calculate overall metrics\n", "    avg_precision = np.mean([h['precision_score'] for h in heuristic_analysis])\n", "    avg_recall = np.mean([h['recall_score'] for h in heuristic_analysis])\n", "    avg_overlap = np.mean([h['semantic_overlap_score'] for h in heuristic_analysis])\n", "\n", "    print(f\"Average Precision: {avg_precision:.1f}%\")\n", "    print(f\"Average Recall: {avg_recall:.1f}%\")\n", "    print(f\"Average Semantic Overlap: {avg_overlap:.1f}%\")\n", "\n", "    # Visualize heuristic accuracy\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "    # Precision and Recall scores\n", "    locations = [h['gt_location'][:20] + '...' for h in heuristic_analysis]\n", "    precision_scores = [h['precision_score'] for h in heuristic_analysis]\n", "    recall_scores = [h['recall_score'] for h in heuristic_analysis]\n", "\n", "    x = np.arange(len(locations))\n", "    width = 0.35\n", "\n", "    ax1.bar(x - width/2, precision_scores, width, label='Precision', color='#3498db')\n", "    ax1.bar(x + width/2, recall_scores, width, label='Recall', color='#e74c3c')\n", "    ax1.set_xlabel('Matched Observations')\n", "    ax1.set_ylabel('Score (%)')\n", "    ax1.set_title('Heuristic Precision & Recall by Observation')\n", "    ax1.set_xticks(x)\n", "    ax1.set_xticklabels(locations, rotation=45, ha='right')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "\n", "    # Semantic overlap scores\n", "    overlap_scores = [h['semantic_overlap_score'] for h in heuristic_analysis]\n", "    bars = ax2.bar(locations, overlap_scores, color='#2ecc71')\n", "    ax2.set_xlabel('Matched Observations')\n", "    ax2.set_ylabel('Overlap Score (%)')\n", "    ax2.set_title('Semantic Overlap of Heuristics')\n", "    ax2.set_xticklabels(locations, rotation=45, ha='right')\n", "    ax2.grid(True, alpha=0.3)\n", "\n", "    # Overall metrics comparison\n", "    metrics = ['Precision', 'Recall', 'Semantic Overlap']\n", "    values = [avg_precision, avg_recall, avg_overlap]\n", "    colors = ['#3498db', '#e74c3c', '#2ecc71']\n", "    bars = ax3.bar(metrics, values, color=colors)\n", "    ax3.set_ylabel('Average Score (%)')\n", "    ax3.set_title('Overall Heuristic Analysis Metrics')\n", "    ax3.set_ylim(0, 100)\n", "\n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, values):\n", "        height = bar.get_height()\n", "        ax3.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "                 f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "    # Distribution of overlap scores\n", "    ax4.hist(overlap_scores, bins=10, color='#9b59b6', alpha=0.7, edgecolor='black')\n", "    ax4.set_xlabel('Semantic Overlap Score (%)')\n", "    ax4.set_ylabel('Frequency')\n", "    ax4.set_title('Distribution of Semantic Overlap Scores')\n", "    ax4.grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"❌ No heuristic analysis results available\")"]}, {"cell_type": "markdown", "metadata": {"id": "Om_rks9r3b8C"}, "source": ["## Step 4: Severity Consistency Analysis\n", "\n", "Compare severity ratings between ground truth and model responses using Gemini."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 503}, "id": "XGNEGCbB3b8C", "executionInfo": {"status": "ok", "timestamp": 1749815472008, "user_tz": -330, "elapsed": 95311, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "1d0bb81c-5e9a-4ab2-8530-5f3a61bc413a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["⚖️ Analyzing severity consistency using Gemini AI...\n", "\n", "🔍 Analyzing severity for match 1/9...\n", "✅ Severity analysis complete - Match: False\n", "\n", "🔍 Analyzing severity for match 2/9...\n", "✅ Severity analysis complete - Match: False\n", "\n", "🔍 Analyzing severity for match 3/9...\n", "✅ Severity analysis complete - Match: False\n", "\n", "🔍 Analyzing severity for match 4/9...\n", "✅ Severity analysis complete - Match: False\n", "\n", "🔍 Analyzing severity for match 5/9...\n", "✅ Severity analysis complete - Match: False\n", "\n", "🔍 Analyzing severity for match 6/9...\n", "✅ Severity analysis complete - Match: False\n", "\n", "🔍 Analyzing severity for match 7/9...\n", "✅ Severity analysis complete - Match: True\n", "\n", "🔍 Analyzing severity for match 8/9...\n", "✅ Severity analysis complete - Match: False\n", "\n", "🔍 Analyzing severity for match 9/9...\n", "✅ Severity analysis complete - Match: False\n"]}], "source": ["def analyze_severity_consistency_with_gemini(matches: List[Dict]) -> Dict:\n", "    \"\"\"Analyze severity consistency using Gemini\"\"\"\n", "    print(\"⚖️ Analyzing severity consistency using Gemini AI...\")\n", "\n", "    severity_results = []\n", "\n", "    for i, match in enumerate(matches):\n", "        gt_severity = match['gt_item']['severity']\n", "        model_severity = match['model_item']['severity']\n", "\n", "        print(f\"\\n🔍 Analyzing severity for match {i+1}/{len(matches)}...\")\n", "\n", "        # Use Gemini to analyze severity appropriateness\n", "        severity_prompt = f\"\"\"\n", "        Analyze the severity rating consistency for this UX issue.\n", "\n", "        Issue Location: {match['gt_item']['location']}\n", "        Issue Description: {match['gt_item']['observation']}\n", "\n", "        Ground Truth Severity: {gt_severity}\n", "        Model Response Severity: {model_severity}\n", "\n", "        Provide analysis in JSON format:\n", "        {{\n", "            \"severity_match\": <true/false>,\n", "            \"severity_appropriateness_score\": <0-100>,\n", "            \"gt_severity_justified\": <true/false>,\n", "            \"model_severity_justified\": <true/false>,\n", "            \"severity_gap_analysis\": \"explanation of any severity differences\",\n", "            \"recommended_severity\": \"High/Medium/Low\",\n", "            \"analysis\": \"detailed analysis of severity consistency\"\n", "        }}\n", "        \"\"\"\n", "\n", "        try:\n", "            response = model.generate_content(severity_prompt)\n", "            json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "            if json_match:\n", "                severity_analysis = json.loads(json_match.group())\n", "                severity_analysis['match_index'] = i\n", "                severity_analysis['gt_severity'] = gt_severity\n", "                severity_analysis['model_severity'] = model_severity\n", "                severity_analysis['gt_location'] = match['gt_item']['location']\n", "                severity_results.append(severity_analysis)\n", "                print(f\"✅ Severity analysis complete - Match: {severity_analysis['severity_match']}\")\n", "            else:\n", "                print(\"❌ Failed to parse Gemini response\")\n", "        except Exception as e:\n", "            print(f\"❌ Error in severity analysis: {e}\")\n", "\n", "    return severity_results\n", "\n", "# Analyze severity consistency\n", "severity_analysis = analyze_severity_consistency_with_gemini(observation_matches)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 785}, "id": "A7F2AtUK3b8D", "executionInfo": {"status": "ok", "timestamp": 1749815472361, "user_tz": -330, "elapsed": 358, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "74c5cf64-f1ce-40ed-db33-283f39562d4d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "⚖️ SEVERITY CONSISTENCY ANALYSIS\n", "========================================\n", "Exact Severity Matches: 1/9 (11.1%)\n", "Average Appropriateness Score: 68.9%\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1500x600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "📋 Detailed Severity Comparison:\n", "❌ Hero section... | GT: High | Model: Medium | Score: 50%\n", "❌ Hero section... | GT: Low | Model: Medium | Score: 85%\n", "❌ One space for everything... | GT: Low | Model: Medium | Score: 85%\n", "❌ Customer stories... | GT: Low | Model: Medium | Score: 75%\n", "❌ get started on Notion... | GT: Low | Model: Medium | Score: 65%\n", "❌ Top nav/Hero section... | GT: High | Model: Medium | Score: 65%\n", "✅ Find anything with one search... | GT: Low | Model: Low | Score: 95%\n", "❌ Nav bar... | GT: High | Model: Low | Score: 15%\n", "❌ AI meeting notes card groups... | GT: Low | Model: Medium | Score: 85%\n"]}], "source": ["# Display severity analysis results\n", "if severity_analysis:\n", "    print(\"\\n⚖️ SEVERITY CONSISTENCY ANALYSIS\")\n", "    print(\"=\" * 40)\n", "\n", "    # Calculate metrics\n", "    exact_matches = sum(1 for s in severity_analysis if s['severity_match'])\n", "    match_percentage = (exact_matches / len(severity_analysis)) * 100 if severity_analysis else 0\n", "    avg_appropriateness = np.mean([s['severity_appropriateness_score'] for s in severity_analysis])\n", "\n", "    print(f\"Exact Severity Matches: {exact_matches}/{len(severity_analysis)} ({match_percentage:.1f}%)\")\n", "    print(f\"Average Appropriateness Score: {avg_appropriateness:.1f}%\")\n", "\n", "    # Create severity comparison visualization\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "    # Severity match pie chart\n", "    match_labels = ['Exact Match', 'Different']\n", "    match_sizes = [exact_matches, len(severity_analysis) - exact_matches]\n", "    match_colors = ['#2ecc71', '#e74c3c']\n", "    ax1.pie(match_sizes, labels=match_labels, colors=match_colors, autopct='%1.1f%%', startangle=90)\n", "    ax1.set_title('Severity Rating Matches', fontsize=14, fontweight='bold')\n", "\n", "    # Appropriateness scores\n", "    locations = [s['gt_location'][:15] + '...' for s in severity_analysis]\n", "    appropriateness_scores = [s['severity_appropriateness_score'] for s in severity_analysis]\n", "    bars = ax2.bar(locations, appropriateness_scores, color='#3498db')\n", "    ax2.set_xlabel('Matched Observations')\n", "    ax2.set_ylabel('Appropriateness Score (%)')\n", "    ax2.set_title('Severity Appropriateness by Observation')\n", "    ax2.set_xticklabels(locations, rotation=45, ha='right')\n", "    ax2.grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # Display detailed severity comparison\n", "    print(\"\\n📋 Detailed Severity Comparison:\")\n", "    for i, s in enumerate(severity_analysis):\n", "        status = \"✅\" if s['severity_match'] else \"❌\"\n", "        print(f\"{status} {s['gt_location'][:30]}... | GT: {s['gt_severity']} | Model: {s['model_severity']} | Score: {s['severity_appropriateness_score']}%\")\n", "else:\n", "    print(\"❌ No severity analysis results available\")"]}, {"cell_type": "markdown", "metadata": {"id": "n7VPDpzV3b8D"}, "source": ["## Step 5: False Positives and False Negatives Analysis\n", "\n", "Identify issues that were hallucinated by the model (false positives) and ground truth issues that were missed (false negatives)."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 225}, "id": "diS86a913b8D", "executionInfo": {"status": "ok", "timestamp": 1749815528194, "user_tz": -330, "elapsed": 55832, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "470a8a25-5284-42ba-fc7d-55da940581b0"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🔍 Analyzing false positives and negatives using Gemini AI...\n", "\n", "📊 Found 1 false negatives (missed GT issues)\n", "📊 Found 3 false positives (model hallucinations)\n", "\n", "🔍 Analyzing false negatives...\n", "✅ FN analysis 1/1 complete\n", "\n", "🔍 Analyzing false positives...\n", "✅ FP analysis 1/3 complete\n", "✅ FP analysis 2/3 complete\n", "✅ FP analysis 3/3 complete\n"]}], "source": ["def analyze_false_positives_negatives_with_gemini(gt_obs: List[Dict], model_obs: List[Dict], matches: List[Dict]) -> Dict:\n", "    \"\"\"Analyze false positives and negatives using Gemini\"\"\"\n", "    print(\"🔍 Analyzing false positives and negatives using Gemini AI...\")\n", "\n", "    # Get matched indices\n", "    matched_gt_indices = {match['gt_index'] for match in matches}\n", "    matched_model_indices = {match['model_index'] for match in matches}\n", "\n", "    # False negatives: GT observations not matched\n", "    false_negatives = [obs for i, obs in enumerate(gt_obs) if i not in matched_gt_indices]\n", "\n", "    # False positives: Model observations not matched\n", "    false_positives = [obs for i, obs in enumerate(model_obs) if i not in matched_model_indices]\n", "\n", "    print(f\"\\n📊 Found {len(false_negatives)} false negatives (missed GT issues)\")\n", "    print(f\"📊 Found {len(false_positives)} false positives (model hallucinations)\")\n", "\n", "    # Analyze false negatives with <PERSON>\n", "    fn_analysis = []\n", "    if false_negatives:\n", "        print(\"\\n🔍 Analyzing false negatives...\")\n", "        for i, fn in enumerate(false_negatives):\n", "            fn_prompt = f\"\"\"\n", "            Analyze why this ground truth UX issue might have been missed by the model.\n", "\n", "            Missed Issue:\n", "            Location: {fn['location']}\n", "            Severity: {fn['severity']}\n", "            Observation: {fn['observation']}\n", "            Heuristics: {fn['heuristics_violated']}\n", "\n", "            Provide analysis in JSON format:\n", "            {{\n", "                \"issue_complexity\": <1-10>,\n", "                \"issue_visibility\": <1-10>,\n", "                \"likely_reasons_missed\": [\"list of reasons\"],\n", "                \"impact_of_missing\": \"High/Medium/Low\",\n", "                \"analysis\": \"detailed analysis of why this was missed\"\n", "            }}\n", "            \"\"\"\n", "\n", "            try:\n", "                response = model.generate_content(fn_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    fn_result = json.loads(json_match.group())\n", "                    fn_result['gt_item'] = fn\n", "                    fn_analysis.append(fn_result)\n", "                    print(f\"✅ FN analysis {i+1}/{len(false_negatives)} complete\")\n", "            except Exception as e:\n", "                print(f\"❌ Error analyzing FN {i+1}: {e}\")\n", "\n", "    # Analyze false positives with <PERSON>\n", "    fp_analysis = []\n", "    if false_positives:\n", "        print(\"\\n🔍 Analyzing false positives...\")\n", "        for i, fp in enumerate(false_positives):\n", "            fp_prompt = f\"\"\"\n", "            Analyze whether this model-generated UX issue is a valid concern or a hallucination.\n", "\n", "            Model Issue:\n", "            Location: {fp['location']}\n", "            Severity: {fp['severity']}\n", "            Observation: {fp['observation']}\n", "            Heuristics: {fp['heuristics_violated']}\n", "\n", "            Provide analysis in JSON format:\n", "            {{\n", "                \"is_valid_issue\": <true/false>,\n", "                \"validity_confidence\": <0-100>,\n", "                \"issue_quality\": <1-10>,\n", "                \"potential_value\": \"High/Medium/Low\",\n", "                \"classification\": \"Valid Addition/Minor Issue/Hallucination\",\n", "                \"analysis\": \"detailed analysis of the issue validity\"\n", "            }}\n", "            \"\"\"\n", "\n", "            try:\n", "                response = model.generate_content(fp_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    fp_result = json.loads(json_match.group())\n", "                    fp_result['model_item'] = fp\n", "                    fp_analysis.append(fp_result)\n", "                    print(f\"✅ FP analysis {i+1}/{len(false_positives)} complete\")\n", "            except Exception as e:\n", "                print(f\"❌ Error analyzing FP {i+1}: {e}\")\n", "\n", "    return {\n", "        'false_negatives': false_negatives,\n", "        'false_positives': false_positives,\n", "        'fn_analysis': fn_analysis,\n", "        'fp_analysis': fp_analysis\n", "    }\n", "\n", "# Analyze false positives and negatives\n", "fp_fn_analysis = analyze_false_positives_negatives_with_gemini(gt_observations, model_observations, observation_matches)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "PR_SqwEh3b8D", "executionInfo": {"status": "ok", "timestamp": 1749815528730, "user_tz": -330, "elapsed": 532, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "e0095801-d193-413b-f8d1-2b7ae4faf821"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "🔍 FALSE POSITIVES & NEGATIVES ANALYSIS\n", "==================================================\n", "\n", "❌ FALSE NEGATIVES (Missed GT Issues): 1\n", "  1. Top nav | Severity: High\n", "     The top navigation bar includes too many options that are unevenly grouped. Items like \"AI,\" \"Enterp...\n", "\n", "➕ FALSE POSITIVES (Model Additions): 3\n", "  1. AI Feature Cards - Arrow icon vertical alignment | Severity: Low\n", "     The right-pointing arrow icons, positioned at the top right of the various AI feature cards (e.g., \"...\n", "  2. Customer Stories Section - Visual Density and Hierarchy | Severity: Medium\n", "     The \"Customer stories\" section presents a significant volume of text-based content in a relatively c...\n", "  3. \"Get started on Notion\" Section - Feature Card Visual Differentiation | Severity: Low\n", "     The three distinct feature cards (\"Notion Mail,\" \"Notion Calendar,\" \"Design system\") within the \"Get...\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1600x1200 with 4 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "📊 DETECTION PERFORMANCE METRICS\n", "========================================\n", "Precision: 0.750 (75.0%)\n", "Recall: 0.900 (90.0%)\n", "F1-Score: 0.818 (81.8%)\n", "Total GT Issues: 10\n", "Total Model Issues: 12\n", "Successfully Matched: 9\n"]}], "source": ["# Display false positives and negatives analysis\n", "print(\"\\n🔍 FALSE POSITIVES & NEGATIVES ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "print(f\"\\n❌ FALSE NEGATIVES (Missed GT Issues): {len(fp_fn_analysis['false_negatives'])}\")\n", "for i, fn in enumerate(fp_fn_analysis['false_negatives']):\n", "    print(f\"  {i+1}. {fn['location']} | Severity: {fn['severity']}\")\n", "    print(f\"     {fn['observation'][:100]}...\")\n", "\n", "print(f\"\\n➕ FALSE POSITIVES (Model Additions): {len(fp_fn_analysis['false_positives'])}\")\n", "for i, fp in enumerate(fp_fn_analysis['false_positives']):\n", "    print(f\"  {i+1}. {fp['location']} | Severity: {fp['severity']}\")\n", "    print(f\"     {fp['observation'][:100]}...\")\n", "\n", "# Visualize false positives and negatives\n", "if fp_fn_analysis['fn_analysis'] or fp_fn_analysis['fp_analysis']:\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "    # False negatives impact analysis\n", "    if fp_fn_analysis['fn_analysis']:\n", "        fn_impacts = [fn['impact_of_missing'] for fn in fp_fn_analysis['fn_analysis']]\n", "        fn_impact_counts = {impact: fn_impacts.count(impact) for impact in ['High', 'Medium', 'Low']}\n", "        ax1.bar(fn_impact_counts.keys(), fn_impact_counts.values(), color=['#e74c3c', '#f39c12', '#2ecc71'])\n", "        ax1.set_title('Impact of Missing Issues (False Negatives)')\n", "        ax1.set_ylabel('Count')\n", "\n", "    # False positives validity analysis\n", "    if fp_fn_analysis['fp_analysis']:\n", "        fp_classifications = [fp['classification'] for fp in fp_fn_analysis['fp_analysis']]\n", "        fp_class_counts = {}\n", "        for classification in fp_classifications:\n", "            fp_class_counts[classification] = fp_class_counts.get(classification, 0) + 1\n", "        ax2.bar(fp_class_counts.keys(), fp_class_counts.values(), color=['#3498db', '#9b59b6', '#e67e22'])\n", "        ax2.set_title('Classification of Model Additions (False Positives)')\n", "        ax2.set_ylabel('Count')\n", "        ax2.tick_params(axis='x', rotation=45)\n", "\n", "    # Overall false positive/negative distribution\n", "    categories = ['True Positives\\n(Matches)', 'False Negatives\\n(Missed)', 'False Positives\\n(Added)']\n", "    values = [len(observation_matches), len(fp_fn_analysis['false_negatives']), len(fp_fn_analysis['false_positives'])]\n", "    colors = ['#2ecc71', '#e74c3c', '#f39c12']\n", "    bars = ax3.bar(categories, values, color=colors)\n", "    ax3.set_title('Overall Issue Detection Performance')\n", "    ax3.set_ylabel('Count')\n", "\n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, values):\n", "        height = bar.get_height()\n", "        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.1,\n", "                 str(value), ha='center', va='bottom', fontweight='bold')\n", "\n", "    # Performance metrics pie chart\n", "    total_gt = len(gt_observations)\n", "    total_model = len(model_observations)\n", "    precision = len(observation_matches) / total_model if total_model > 0 else 0\n", "    recall = len(observation_matches) / total_gt if total_gt > 0 else 0\n", "    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n", "\n", "    metrics = ['Precision', 'Recall', 'F1-Score']\n", "    metric_values = [precision * 100, recall * 100, f1_score * 100]\n", "    bars = ax4.bar(metrics, metric_values, color=['#3498db', '#e74c3c', '#2ecc71'])\n", "    ax4.set_title('Overall Detection Metrics')\n", "    ax4.set_ylabel('Score (%)')\n", "    ax4.set_ylim(0, 100)\n", "\n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, metric_values):\n", "        height = bar.get_height()\n", "        ax4.text(bar.get_x() + bar.get_width()/2., height + 1,\n", "                 f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "print(f\"\\n📊 DETECTION PERFORMANCE METRICS\")\n", "print(\"=\" * 40)\n", "total_gt = len(gt_observations)\n", "total_model = len(model_observations)\n", "matches_count = len(observation_matches)\n", "precision = matches_count / total_model if total_model > 0 else 0\n", "recall = matches_count / total_gt if total_gt > 0 else 0\n", "f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n", "\n", "print(f\"Precision: {precision:.3f} ({precision*100:.1f}%)\")\n", "print(f\"Recall: {recall:.3f} ({recall*100:.1f}%)\")\n", "print(f\"F1-Score: {f1_score:.3f} ({f1_score*100:.1f}%)\")\n", "print(f\"Total GT Issues: {total_gt}\")\n", "print(f\"Total Model Issues: {total_model}\")\n", "print(f\"Successfully Matched: {matches_count}\")"]}, {"cell_type": "markdown", "metadata": {"id": "ld-ApurF3b8D"}, "source": ["## Step 6: Overall Evaluation Summary Using Gemini\n", "\n", "Generate a comprehensive evaluation summary using all the analysis results."]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "oluhUPYL3b8D", "executionInfo": {"status": "ok", "timestamp": 1749815798648, "user_tz": -330, "elapsed": 16802, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "d90c2ba4-69cf-4c6a-fd1c-517f368cc08d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["📝 Generating overall evaluation summary using Gemini AI...\n", "\n", "================================================================================\n", "📋 COMPREHENSIVE EVALUATION SUMMARY\n", "================================================================================\n", "## UX Audit Model Performance Evaluation Summary\n", "\n", "**Date:** October 26, 2023\n", "**To:** Model Development Team; UX Lead\n", "**From:** [Your Name/Department]\n", "**Subject:** Comprehensive Evaluation of UX Audit Model Performance\n", "\n", "---\n", "\n", "### **1. Overall Model Performance Assessment**\n", "\n", "The UX audit model demonstrates promising capabilities in its ability to identify a high percentage of existing usability issues. Its strong observation coverage indicates a robust foundational ability to detect problems. However, the model exhibits significant limitations in the precision of its findings and, most critically, in its ability to consistently assign appropriate severity levels to identified issues. While it serves as a valuable initial detection tool, its current state necessitates substantial human oversight and correction to ensure accuracy and actionable insights.\n", "\n", "### **2. Key Strengths Identified**\n", "\n", "*   **Outstanding Observation Coverage:** The model successfully identified 9 out of 10 ground truth issues, achieving an impressive 90.0% coverage. This indicates a highly effective capability to detect a significant proportion of actual UX issues present within the audited system.\n", "*   **Low False Negatives:** With only 1 false negative (missed issue), the model demonstrates a strong ability to avoid overlooking critical problems, which is crucial for a comprehensive audit.\n", "*   **Good Recall (Relative to Coverage):** The average recall of 59.2%, coupled with the high coverage, suggests the model is generally good at 'finding' issues that exist, even if its precision in classifying them needs work.\n", "\n", "### **3. Major Weaknesses and Areas for Improvement**\n", "\n", "*   **Critically Low Severity Consistency:** This is the most significant weakness. An exact severity match rate of only 11.1% is a major concern. Without reliable severity assignment, the model's findings are difficult to prioritize and action, severely limiting its utility for stakeholders and requiring extensive manual review and re-classification.\n", "*   **Moderate Precision and High False Positives:** An average precision of 55.6% implies that nearly half of the issues identified by the model are either incorrect or miscategorized. The presence of 3 false positives (issues identified by the model that were not actual ground truth issues) out of 12 total model issues supports this, indicating a notable 'noise' factor that can lead to wasted effort in investigating non-existent problems.\n", "*   **Heuristic Accuracy Limitations:** The combined moderate precision and recall suggest the model struggles to accurately classify issues under specific heuristic categories, potentially leading to ambiguity in understanding the root cause of the problem.\n", "\n", "### **4. Specific Recommendations for Model Enhancement**\n", "\n", "1.  **Prioritize Severity Assignment Refinement:**\n", "    *   **Enhanced Training Data:** Collect a larger and more diverse dataset specifically annotated with nuanced severity levels, possibly using a multi-expert consensus approach.\n", "    *   **Contextual Understanding:** Develop features or incorporate advanced NLP/vision models that can better understand the context and impact of an issue on user flow, task completion, and overall experience.\n", "    *   **Rule-Based Augmentation:** Consider augmenting the machine learning model with a rule-based system for severity assignment, especially for common patterns or critical issues.\n", "    *   **Ordinal Regression:** Explore machine learning models specifically designed for ordinal classification (where severity has an order: low < medium < high).\n", "\n", "2.  **Improve Precision and Reduce False Positives:**\n", "    *   **Refine Classification Thresholds:** Implement tunable confidence thresholds for issue detection to allow for a trade-off between recall and precision.\n", "    *   **Negative Sampling:** Explicitly include \"non-issue\" examples in the training data to help the model learn what *not* to flag as a UX problem.\n", "    *   **Feature Engineering:** Enhance features that distinguish genuine UX issues from design elements or minor anomalies.\n", "    *   **Anomaly Detection:** Integrate anomaly detection techniques to better identify true outliers that represent issues.\n", "\n", "3.  **Expand and Diversify Heuristic Training Data:**\n", "    *   Increase the volume and variety of examples for each heuristic principle to improve the model's ability to accurately map issues to specific heuristics.\n", "    *   Focus on more complex or nuanced heuristic violations in training data.\n", "\n", "4.  **Implement a Human-in-the-Loop (HITL) Feedback Mechanism:**\n", "    *   Develop a system where human experts can easily correct the model's classifications, severity assignments, and false positives/negatives. This feedback should then be systematically used to retrain and improve the model iteratively.\n", "\n", "### **5. Comparative Analysis Against Expected Performance**\n", "\n", "For an automated UX audit model, general performance expectations often include:\n", "\n", "*   **Observation Coverage/Recall:** Aims for 80%+, which the model significantly surpasses at 90%. This is a strong positive.\n", "*   **Precision:** Ideally, 70%+ for reliable output. The model's 55.6% falls short of this, indicating a need for substantial improvement to reduce noise.\n", "*   **Severity Consistency:** Aims for 50%+ for moderate utility, with 80%+ being excellent. The model's 11.1% is significantly below any acceptable threshold, rendering its severity predictions largely unusable without manual override.\n", "*   **False Positives/Negatives:** Aim for minimal. The 1 false negative is excellent. The 3 false positives (representing 25% of model-identified issues) are higher than desired, contributing to the lower precision.\n", "\n", "In summary, the model meets or slightly exceeds expectations in its ability to *identify* the majority of issues (coverage). However, it significantly falls short of expectations regarding the *accuracy and utility* of those identified issues, particularly in precision and, most critically, severity assessment.\n", "\n", "### **6. Final Grade/Rating**\n", "\n", "**Grade: C-**\n", "\n", "**Justification:** The model's exceptional observation coverage is a significant asset, demonstrating its fundamental capability to detect issues. However, this strength is heavily offset by its considerable struggles with precision and, most notably, the critically low severity consistency. While the model can serve as a helpful \"first pass\" tool to ensure most issues are caught, the low accuracy in classification and severity assignment necessitates extensive human review and correction for almost every finding. This significantly limits its overall efficiency, reliability, and immediate actionability as a standalone solution for automated UX auditing. Substantial improvements are required in the areas of precision and severity prediction to elevate its performance to a level that provides truly valuable and actionable insights.\n", "================================================================================\n"]}], "source": ["def generate_overall_evaluation_summary(coverage_results, heuristic_analysis, severity_analysis, fp_fn_analysis, observation_matches) -> str:\n", "    \"\"\"Generate comprehensive evaluation summary using Gemini\"\"\"\n", "    print(\"📝 Generating overall evaluation summary using Gemini AI...\")\n", "\n", "    # Prepare summary data\n", "    total_gt = len(gt_observations)\n", "    total_model = len(model_observations)\n", "    matches_count = len(observation_matches)\n", "\n", "    # Calculate metrics\n", "    coverage_pct = coverage_results['coverage_percentage']\n", "    avg_heuristic_precision = np.mean([h['precision_score'] for h in heuristic_analysis]) if heuristic_analysis else 0\n", "    avg_heuristic_recall = np.mean([h['recall_score'] for h in heuristic_analysis]) if heuristic_analysis else 0\n", "    severity_match_pct = (sum(1 for s in severity_analysis if s['severity_match']) / len(severity_analysis) * 100) if severity_analysis else 0\n", "\n", "    summary_prompt = f\"\"\"\n", "    Generate a comprehensive evaluation summary for a UX audit model performance.\n", "\n", "    EVALUATION RESULTS:\n", "\n", "    1. OBSERVATION COVERAGE:\n", "    - Total Ground Truth Issues: {total_gt}\n", "    - Total Model Issues: {total_model}\n", "    - Successfully Matched: {matches_count}\n", "    - Coverage Percentage: {coverage_pct:.1f}%\n", "\n", "    2. HEURISTIC ACCURACY:\n", "    - Average Precision: {avg_heuristic_precision:.1f}%\n", "    - Average Recall: {avg_heuristic_recall:.1f}%\n", "\n", "    3. SEVERITY CONSISTENCY:\n", "    - Exact Severity Matches: {severity_match_pct:.1f}%\n", "\n", "    4. FALSE POSITIVES/NEGATIVES:\n", "    - False Negatives (Missed): {len(fp_fn_analysis['false_negatives'])}\n", "    - False Positives (Added): {len(fp_fn_analysis['false_positives'])}\n", "\n", "    Provide a comprehensive evaluation summary including:\n", "    1. Overall model performance assessment\n", "    2. Key strengths identified\n", "    3. Major weaknesses and areas for improvement\n", "    4. Specific recommendations for model enhancement\n", "    5. Comparative analysis against expected performance\n", "    6. Final grade/rating (A-F scale)\n", "\n", "    Format as a detailed professional evaluation report.\n", "    \"\"\"\n", "\n", "    try:\n", "        response = model.generate_content(summary_prompt)\n", "        return response.text\n", "    except Exception as e:\n", "        return f\"Error generating summary: {e}\"\n", "\n", "# Generate overall evaluation summary\n", "evaluation_summary = generate_overall_evaluation_summary(\n", "    coverage_results, heuristic_analysis, severity_analysis, fp_fn_analysis, observation_matches\n", ")\n", "\n", "print(\"\\n\" + \"=\" * 80)\n", "print(\"📋 COMPREHENSIVE EVALUATION SUMMARY\")\n", "print(\"=\" * 80)\n", "print(evaluation_summary)\n", "print(\"=\" * 80)"]}, {"cell_type": "markdown", "metadata": {"id": "kC3n8d3U3b8D"}, "source": ["## Step 7: Export Results\n", "\n", "Save all evaluation results to JSON files for further analysis."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Plel1rSU3b8E", "executionInfo": {"status": "ok", "timestamp": 1749815819597, "user_tz": -330, "elapsed": 18, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "9c62d0b5-bd2c-492a-d722-f33891884e6c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "💾 Evaluation results saved to: ux_audit_evaluation_results_20250613_115659.json\n", "\n", "✅ UX Audit Evaluation Complete!\n", "\n", "📊 FINAL SUMMARY:\n", "   • Coverage: 90.0%\n", "   • Precision: 75.0%\n", "   • Recall: 90.0%\n", "   • F1-Score: 81.8%\n", "   • Heuristic Precision: 55.6%\n", "   • Severity Consistency: 11.1%\n"]}], "source": ["# Compile all results\n", "final_results = {\n", "    'evaluation_metadata': {\n", "        'timestamp': pd.Timestamp.now().isoformat(),\n", "        'total_gt_observations': len(gt_observations),\n", "        'total_model_observations': len(model_observations),\n", "        'evaluation_parameters': [\n", "            'Observation Coverage',\n", "            'Heuristic Match Accuracy',\n", "            'Severity Consistency',\n", "            'Location Accuracy',\n", "            'Observation Similarity',\n", "            'False Positives/Hallucinations',\n", "            'False Negatives/Omissions',\n", "            'Overall Evaluation Summary'\n", "        ]\n", "    },\n", "    'observation_coverage': coverage_results,\n", "    'observation_matches': observation_matches,\n", "    'heuristic_analysis': heuristic_analysis,\n", "    'severity_analysis': severity_analysis,\n", "    'false_positives_negatives': fp_fn_analysis,\n", "    'evaluation_summary': evaluation_summary,\n", "    'performance_metrics': {\n", "        'precision': matches_count / total_model if total_model > 0 else 0,\n", "        'recall': matches_count / total_gt if total_gt > 0 else 0,\n", "        'f1_score': f1_score,\n", "        'coverage_percentage': coverage_results['coverage_percentage'] if coverage_results else 0,\n", "        'severity_match_percentage': (sum(1 for s in severity_analysis if s['severity_match']) / len(severity_analysis) * 100) if severity_analysis else 0,\n", "        'avg_heuristic_precision': np.mean([h['precision_score'] for h in heuristic_analysis]) if heuristic_analysis else 0,\n", "        'avg_heuristic_recall': np.mean([h['recall_score'] for h in heuristic_analysis]) if heuristic_analysis else 0\n", "    }\n", "}\n", "\n", "# Save results to JSON file\n", "output_filename = f'ux_audit_evaluation_results_{pd.Timestamp.now().strftime(\"%Y%m%d_%H%M%S\")}.json'\n", "with open(output_filename, 'w', encoding='utf-8') as f:\n", "    json.dump(final_results, f, indent=2, ensure_ascii=False, default=str)\n", "\n", "print(f\"\\n💾 Evaluation results saved to: {output_filename}\")\n", "print(\"\\n✅ UX Audit Evaluation Complete!\")\n", "print(\"\\n📊 FINAL SUMMARY:\")\n", "print(f\"   • Coverage: {final_results['performance_metrics']['coverage_percentage']:.1f}%\")\n", "print(f\"   • Precision: {(final_results['performance_metrics']['precision'] * 100):.1f}%\")\n", "print(f\"   • Recall: {(final_results['performance_metrics']['recall'] * 100):.1f}%\")\n", "print(f\"   • F1-Score: {(final_results['performance_metrics']['f1_score'] * 100):.1f}%\")\n", "print(f\"   • Heuristic Precision: {final_results['performance_metrics']['avg_heuristic_precision']:.1f}%\")\n", "print(f\"   • Severity Consistency: {final_results['performance_metrics']['severity_match_percentage']:.1f}%\")"]}, {"cell_type": "code", "source": [], "metadata": {"id": "MxkDuAt37FcS"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 243}, "id": "44cdb024", "executionInfo": {"status": "ok", "timestamp": 1749815819563, "user_tz": -330, "elapsed": 20912, "user": {"displayName": "<PERSON><PERSON>", "userId": "08746250310600045750"}}, "outputId": "65ef16f7-eef7-4ab2-b231-11b03e840b1e"}, "source": ["# Generate overall evaluation summary\n", "evaluation_summary = generate_overall_evaluation_summary(\n", "    coverage_results, heuristic_analysis, severity_analysis, fp_fn_analysis, observation_matches\n", ")\n", "\n", "# Compile all results\n", "final_results = {\n", "    'evaluation_metadata': {\n", "        'timestamp': pd.Timestamp.now().isoformat(),\n", "        'total_gt_observations': len(gt_observations),\n", "        'total_model_observations': len(model_observations),\n", "        'evaluation_parameters': [\n", "            'Observation Coverage',\n", "            'Heuristic Match Accuracy',\n", "            'Severity Consistency',\n", "            'Location Accuracy',\n", "            'Observation Similarity',\n", "            'False Positives/Hallucinations',\n", "            'False Negatives/Omissions',\n", "            'Overall Evaluation Summary'\n", "        ]\n", "    },\n", "    'observation_coverage': coverage_results,\n", "    'observation_matches': observation_matches,\n", "    'heuristic_analysis': heuristic_analysis,\n", "    'severity_analysis': severity_analysis,\n", "    'false_positives_negatives': fp_fn_analysis,\n", "    'evaluation_summary': evaluation_summary,\n", "    'performance_metrics': {\n", "        'precision': matches_count / total_model if total_model > 0 else 0,\n", "        'recall': matches_count / total_gt if total_gt > 0 else 0,\n", "        'f1_score': f1_score,\n", "        'coverage_percentage': coverage_results['coverage_percentage'] if coverage_results else 0,\n", "        'severity_match_percentage': (sum(1 for s in severity_analysis if s['severity_match']) / len(severity_analysis) * 100) if severity_analysis else 0,\n", "        'avg_heuristic_precision': np.mean([h['precision_score'] for h in heuristic_analysis]) if heuristic_analysis else 0,\n", "        'avg_heuristic_recall': np.mean([h['recall_score'] for h in heuristic_analysis]) if heuristic_analysis else 0\n", "    }\n", "}\n", "\n", "# Save results to JSON file\n", "output_filename = f'ux_audit_evaluation_results_{pd.Timestamp.now().strftime(\"%Y%m%d_%H%M%S\")}.json'\n", "with open(output_filename, 'w', encoding='utf-8') as f:\n", "    json.dump(final_results, f, indent=2, ensure_ascii=False, default=str)\n", "\n", "print(f\"\\n💾 Evaluation results saved to: {output_filename}\")\n", "print(\"\\n✅ UX Audit Evaluation Complete!\")\n", "print(\"\\n📊 FINAL SUMMARY:\")\n", "print(f\"   • Coverage: {final_results['performance_metrics']['coverage_percentage']:.1f}%\")\n", "print(f\"   • Precision: {(final_results['performance_metrics']['precision'] * 100):.1f}%\")\n", "print(f\"   • Recall: {(final_results['performance_metrics']['recall'] * 100):.1f}%\")\n", "print(f\"   • F1-Score: {(final_results['performance_metrics']['f1_score'] * 100):.1f}%\")\n", "print(f\"   • Heuristic Precision: {final_results['performance_metrics']['avg_heuristic_precision']:.1f}%\")\n", "print(f\"   • Severity Consistency: {final_results['performance_metrics']['severity_match_percentage']:.1f}%\")"], "execution_count": 17, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["📝 Generating overall evaluation summary using Gemini AI...\n", "\n", "💾 Evaluation results saved to: ux_audit_evaluation_results_20250613_115659.json\n", "\n", "✅ UX Audit Evaluation Complete!\n", "\n", "📊 FINAL SUMMARY:\n", "   • Coverage: 90.0%\n", "   • Precision: 75.0%\n", "   • Recall: 90.0%\n", "   • F1-Score: 81.8%\n", "   • Heuristic Precision: 55.6%\n", "   • Severity Consistency: 11.1%\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "2Jzm4NxwJgia"}, "execution_count": null, "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}