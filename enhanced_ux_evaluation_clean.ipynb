{"cells": [{"cell_type": "markdown", "metadata": {"id": "enhanced_ux_eval_title"}, "source": ["# Enhanced UX Audit Report Evaluation Using Gemini AI\n", "\n", "This notebook provides comprehensive evaluation of model-generated UX audit reports against ground truth data using Google's Gemini AI model.\n", "\n", "## Key Features:\n", "1. **Coverage Score** - Location and observation overlap analysis\n", "2. **Observation Score** (Most Important) - Key points extraction and semantic comparison with detailed reasoning\n", "3. **Location Score** (Second Most Important) - Accurate UI area identification with justification\n", "4. **Heuristics Score** - Matched, unmatched, and extra heuristics analysis\n", "5. **Severity Score** - Exact and near-match comparison with justification\n", "6. **Detailed CSV Output** - Comprehensive results with reasoning for each score\n", "\n", "All scores include detailed explanations generated by Gemini for transparency and audit purposes."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_packages"}, "outputs": [], "source": ["# Install required packages\n", "!pip install google-generativeai pandas numpy mat<PERSON><PERSON><PERSON>b seaborn scikit-learn"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "import_libraries"}, "outputs": [], "source": ["# Import required libraries\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import Dict, List, Tuple, Any, Optional\n", "import google.generativeai as genai\n", "import re\n", "from collections import defaultdict\n", "import warnings\n", "import time\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "configure_gemini"}, "outputs": [], "source": ["# Configure Gemini API\n", "# Replace 'YOUR_API_KEY' with your actual Gemini API key\n", "GEMINI_API_KEY = 'YOUR_API_KEY'  # Get from https://makersuite.google.com/app/apikey\n", "genai.configure(api_key=GEMINI_API_KEY)\n", "\n", "# Initialize Gemini model\n", "model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')\n", "\n", "print(\"✅ Gemini AI configured successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data"}, "outputs": [], "source": ["# Load data files\n", "def load_json_data(file_path: str) -> Dict:\n", "    \"\"\"Load JSON data from file\"\"\"\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            return json.load(f)\n", "    except Exception as e:\n", "        print(f\"Error loading {file_path}: {e}\")\n", "        return {}\n", "\n", "# Load ground truth and model response data\n", "ground_truth = load_json_data('ground_truth.json')\n", "model_response = load_json_data('model_response.json')\n", "\n", "# Extract observations\n", "gt_observations = ground_truth.get('audit_report', {}).get('observations', [])\n", "model_observations = model_response.get('audit_report', {}).get('observations', [])\n", "\n", "print(f\"📊 Loaded {len(gt_observations)} ground truth observations\")\n", "print(f\"📊 Loaded {len(model_observations)} model response observations\")\n", "print(\"\\n✅ Data loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "gemini_functions_header"}, "source": ["## Gemini AI Helper Functions\n", "\n", "These functions use Gemini to extract key points, calculate semantic similarity, and provide detailed reasoning for each score."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gemini_helper_functions"}, "outputs": [], "source": ["def extract_key_points_with_gemini(observation_text: str) -> Dict:\n", "    \"\"\"Extract key points from observation text using Gemini\"\"\"\n", "    prompt = f\"\"\"\n", "    Extract the core key points from this UX audit observation. Focus on:\n", "    1. Main usability issues identified\n", "    2. Specific UI elements mentioned\n", "    3. User impact described\n", "    4. Design problems highlighted\n", "    \n", "    Observation: {observation_text}\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"key_points\": [\"point1\", \"point2\", \"point3\"],\n", "        \"main_issue\": \"primary usability concern\",\n", "        \"ui_elements\": [\"element1\", \"element2\"],\n", "        \"user_impact\": \"how this affects users\"\n", "    }}\n", "    \"\"\"\n", "    \n", "    try:\n", "        response = model.generate_content(prompt)\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            return json.loads(json_match.group())\n", "        else:\n", "            return {\"key_points\": [], \"main_issue\": \"\", \"ui_elements\": [], \"user_impact\": \"\"}\n", "    except Exception as e:\n", "        print(f\"Error extracting key points: {e}\")\n", "        return {\"key_points\": [], \"main_issue\": \"\", \"ui_elements\": [], \"user_impact\": \"\"}\n", "\n", "def calculate_observation_score_with_reasoning(gt_key_points: Dict, model_key_points: Dict, \n", "                                               gt_text: str, model_text: str) -> Dict:\n", "    \"\"\"Calculate observation similarity score with detailed reasoning using Gemini\"\"\"\n", "    prompt = f\"\"\"\n", "    Compare these two UX audit observations and their extracted key points. Provide a detailed analysis.\n", "    \n", "    GROUND TRUTH:\n", "    Text: {gt_text[:300]}...\n", "    Key Points: {gt_key_points.get('key_points', [])}\n", "    Main Issue: {gt_key_points.get('main_issue', '')}\n", "    UI Elements: {gt_key_points.get('ui_elements', [])}\n", "    \n", "    MODEL RESPONSE:\n", "    Text: {model_text[:300]}...\n", "    Key Points: {model_key_points.get('key_points', [])}\n", "    Main Issue: {model_key_points.get('main_issue', '')}\n", "    UI Elements: {model_key_points.get('ui_elements', [])}\n", "    \n", "    Analyze semantic and contextual alignment. Consider:\n", "    1. How many key points overlap or are semantically similar?\n", "    2. Do both identify the same core usability issue?\n", "    3. Are the UI elements correctly identified?\n", "    4. Is the user impact assessment similar?\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"observation_score\": 75,\n", "        \"key_points_overlap\": 80,\n", "        \"main_issue_match\": 70,\n", "        \"ui_elements_match\": 85,\n", "        \"detailed_reasoning\": \"Comprehensive explanation of why this score was assigned, mentioning specific key phrases and contextual elements that led to this assessment. Explain what matched, what didn't, and why.\",\n", "        \"matching_concepts\": [\"concept1\", \"concept2\"],\n", "        \"missing_concepts\": [\"concept1\", \"concept2\"]\n", "    }}\n", "    \"\"\"\n", "    \n", "    try:\n", "        response = model.generate_content(prompt)\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            return json.loads(json_match.group())\n", "        else:\n", "            return {\"observation_score\": 0, \"detailed_reasoning\": \"Failed to parse response\"}\n", "    except Exception as e:\n", "        print(f\"Error calculating observation score: {e}\")\n", "        return {\"observation_score\": 0, \"detailed_reasoning\": f\"API Error: {e}\"}"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "location_heuristics_functions"}, "outputs": [], "source": ["def calculate_location_score_with_reasoning(gt_location: str, model_location: str) -> Dict:\n", "    \"\"\"Calculate location similarity score with detailed reasoning\"\"\"\n", "    prompt = f\"\"\"\n", "    Compare these two UI location descriptions for a UX audit. Determine how accurately the model identified the same UI area.\n", "    \n", "    Ground Truth Location: {gt_location}\n", "    Model Response Location: {model_location}\n", "    \n", "    Consider:\n", "    1. Do they refer to the same UI component/section?\n", "    2. Is the specificity level appropriate?\n", "    3. Are there semantic similarities even if wording differs?\n", "    4. Would a user be able to find the same area based on both descriptions?\n", "    \n", "    Be strict in your assessment - location accuracy is critical for UX audits.\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"location_score\": 85,\n", "        \"exact_match\": false,\n", "        \"semantic_match\": true,\n", "        \"specificity_match\": true,\n", "        \"detailed_reasoning\": \"Explain why this location match was considered accurate or inaccurate. Mention specific terms, UI components, and why the match quality justifies this score.\"\n", "    }}\n", "    \"\"\"\n", "    \n", "    try:\n", "        response = model.generate_content(prompt)\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            return json.loads(json_match.group())\n", "        else:\n", "            return {\"location_score\": 0, \"detailed_reasoning\": \"Failed to parse response\"}\n", "    except Exception as e:\n", "        print(f\"Error calculating location score: {e}\")\n", "        return {\"location_score\": 0, \"detailed_reasoning\": f\"API Error: {e}\"}\n", "\n", "def calculate_heuristics_score_with_reasoning(gt_heuristics: List[str], model_heuristics: List[str]) -> Dict:\n", "    \"\"\"Calculate heuristics match score with detailed reasoning\"\"\"\n", "    prompt = f\"\"\"\n", "    Compare these two lists of violated heuristics from UX audit observations.\n", "    \n", "    Ground Truth Heuristics: {gt_heuristics}\n", "    Model Response Heuristics: {model_heuristics}\n", "    \n", "    Analyze:\n", "    1. Exact matches between the lists\n", "    2. Semantic matches (different wording, same concept)\n", "    3. Missing heuristics (in GT but not in model)\n", "    4. Extra heuristics (in model but not in GT)\n", "    5. Relevance and importance of each category\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"heuristics_score\": 70,\n", "        \"matched_heuristics\": [\"heuristic1\", \"heuristic2\"],\n", "        \"unmatched_heuristics\": [\"heuristic1\", \"heuristic2\"],\n", "        \"extra_heuristics\": [\"heuristic1\", \"heuristic2\"],\n", "        \"semantic_matches\": [[\"gt_heuristic\", \"model_heuristic\"]],\n", "        \"detailed_reasoning\": \"Explain the match logic, impact of missing heuristics, relevance of extra heuristics, and why this score reflects the quality of heuristic identification.\"\n", "    }}\n", "    \"\"\"\n", "    \n", "    try:\n", "        response = model.generate_content(prompt)\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            return json.loads(json_match.group())\n", "        else:\n", "            return {\"heuristics_score\": 0, \"detailed_reasoning\": \"Failed to parse response\"}\n", "    except Exception as e:\n", "        print(f\"Error calculating heuristics score: {e}\")\n", "        return {\"heuristics_score\": 0, \"detailed_reasoning\": f\"API Error: {e}\"}\n", "\n", "def calculate_severity_score_with_reasoning(gt_severity: str, model_severity: str) -> Dict:\n", "    \"\"\"Calculate severity match score with detailed reasoning\"\"\"\n", "    prompt = f\"\"\"\n", "    Compare these two severity ratings from UX audit observations.\n", "    \n", "    Ground Truth Severity: {gt_severity}\n", "    Model Response Severity: {model_severity}\n", "    \n", "    Consider:\n", "    1. Exact match (same severity level)\n", "    2. Near match (one level difference, e.g., High vs Medium)\n", "    3. Severity scale: Low < Medium < High\n", "    4. Impact of severity mismatch on audit quality\n", "    \n", "    Respond in JSON format:\n", "    {{\n", "        \"severity_score\": 90,\n", "        \"exact_match\": true,\n", "        \"near_match\": false,\n", "        \"severity_difference\": 0,\n", "        \"detailed_reasoning\": \"Explain why this severity comparison received this score, considering the impact of severity accuracy on prioritization and resource allocation.\"\n", "    }}\n", "    \"\"\"\n", "    \n", "    try:\n", "        response = model.generate_content(prompt)\n", "        json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "        if json_match:\n", "            return json.loads(json_match.group())\n", "        else:\n", "            return {\"severity_score\": 0, \"detailed_reasoning\": \"Failed to parse response\"}\n", "    except Exception as e:\n", "        print(f\"Error calculating severity score: {e}\")\n", "        return {\"severity_score\": 0, \"detailed_reasoning\": f\"API Error: {e}\"}"]}, {"cell_type": "markdown", "metadata": {"id": "coverage_analysis_header"}, "source": ["## Coverage Analysis and Matching\n", "\n", "This section implements the coverage logic to determine which GT observations have corresponding model responses based on location and observation similarity."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "coverage_matching_functions"}, "outputs": [], "source": ["def find_coverage_matches(gt_observations: List[Dict], model_observations: List[Dict]) -> List[Dict]:\n", "    \"\"\"Find coverage matches between GT and model observations using Gemini\"\"\"\n", "    print(\"🔍 Finding coverage matches using location and observation similarity...\")\n", "    print(f\"📊 Processing {len(gt_observations)} GT observations vs {len(model_observations)} model observations\")\n", "    \n", "    coverage_matches = []\n", "    \n", "    for i, gt_obs in enumerate(gt_observations):\n", "        print(f\"\\n📍 Processing GT observation {i+1}/{len(gt_observations)}: {gt_obs['location'][:50]}...\")\n", "        \n", "        best_match = None\n", "        best_score = 0\n", "        \n", "        # Compare with each model observation\n", "        for j, model_obs in enumerate(model_observations):\n", "            # Quick location similarity check first\n", "            location_prompt = f\"\"\"\n", "            Compare these two UI locations for overlap:\n", "            Location 1: {gt_obs['location']}\n", "            Location 2: {model_obs['location']}\n", "            \n", "            Do they refer to the same or overlapping UI area? Consider semantic similarity.\n", "            \n", "            Respond in JSON format:\n", "            {{\n", "                \"location_overlap\": 75,\n", "                \"same_area\": true\n", "            }}\n", "            \"\"\"\n", "            \n", "            try:\n", "                response = model.generate_content(location_prompt)\n", "                json_match = re.search(r'\\{.*\\}', response.text, re.DOTALL)\n", "                if json_match:\n", "                    location_result = json.loads(json_match.group())\n", "                    location_overlap = location_result.get('location_overlap', 0)\n", "                    \n", "                    # If location has some overlap, check observation similarity\n", "                    if location_overlap > 30:  # Threshold for location overlap\n", "                        observation_prompt = f\"\"\"\n", "                        Compare these two UX observations for contextual similarity:\n", "                        Observation 1: {gt_obs['observation'][:200]}...\n", "                        Observation 2: {model_obs['observation'][:200]}...\n", "                        \n", "                        Do they identify similar usability issues or concerns?\n", "                        \n", "                        Respond in JSON format:\n", "                        {{\n", "                            \"observation_similarity\": 80,\n", "                            \"similar_issue\": true\n", "                        }}\n", "                        \"\"\"\n", "                        \n", "                        obs_response = model.generate_content(observation_prompt)\n", "                        obs_json_match = re.search(r'\\{.*\\}', obs_response.text, re.DOTALL)\n", "                        if obs_json_match:\n", "                            obs_result = json.loads(obs_json_match.group())\n", "                            obs_similarity = obs_result.get('observation_similarity', 0)\n", "                            \n", "                            # Combined score (weighted: location 40%, observation 60%)\n", "                            combined_score = (location_overlap * 0.4) + (obs_similarity * 0.6)\n", "                            \n", "                            if combined_score > best_score and combined_score > 50:  # Coverage threshold\n", "                                best_score = combined_score\n", "                                best_match = {\n", "                                    'gt_index': i,\n", "                                    'model_index': j,\n", "                                    'gt_observation': gt_obs,\n", "                                    'model_observation': model_obs,\n", "                                    'location_overlap': location_overlap,\n", "                                    'observation_similarity': obs_similarity,\n", "                                    'combined_score': combined_score\n", "                                }\n", "                                \n", "                time.sleep(0.5)  # Rate limiting\n", "                \n", "            except Exception as e:\n", "                print(f\"  ❌ Error comparing with model {j}: {e}\")\n", "                continue\n", "        \n", "        if best_match:\n", "            coverage_matches.append(best_match)\n", "            print(f\"  ✅ Match found: Model {best_match['model_index']} (Score: {best_match['combined_score']:.1f})\")\n", "        else:\n", "            print(f\"  ❌ No coverage match found\")\n", "    \n", "    print(f\"\\n🎯 Coverage Analysis Complete: {len(coverage_matches)}/{len(gt_observations)} GT observations covered\")\n", "    print(f\"📊 Coverage Percentage: {len(coverage_matches)/len(gt_observations)*100:.1f}%\")\n", "    \n", "    return coverage_matches"]}, {"cell_type": "markdown", "metadata": {"id": "main_evaluation_header"}, "source": ["## Main Evaluation Function\n", "\n", "This function orchestrates the complete evaluation process, calculating all scores for covered observations only."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "main_evaluation_function"}, "outputs": [], "source": ["def evaluate_covered_observations(coverage_matches: List[Dict]) -> List[Dict]:\n", "    \"\"\"Evaluate all scores for covered observations only\"\"\"\n", "    print(\"\\n🔬 Starting detailed evaluation of covered observations...\")\n", "    print(f\"📊 Evaluating {len(coverage_matches)} covered observations\")\n", "    \n", "    evaluation_results = []\n", "    \n", "    for i, match in enumerate(coverage_matches):\n", "        print(f\"\\n📋 Evaluating match {i+1}/{len(coverage_matches)}...\")\n", "        \n", "        gt_obs = match['gt_observation']\n", "        model_obs = match['model_observation']\n", "        \n", "        # Extract key points for both observations\n", "        print(\"  🔍 Extracting key points...\")\n", "        gt_key_points = extract_key_points_with_gemini(gt_obs['observation'])\n", "        model_key_points = extract_key_points_with_gemini(model_obs['observation'])\n", "        \n", "        time.sleep(1)  # Rate limiting\n", "        \n", "        # Calculate observation score (most important)\n", "        print(\"  📊 Calculating observation score...\")\n", "        observation_score = calculate_observation_score_with_reasoning(\n", "            gt_key_points, model_key_points, gt_obs['observation'], model_obs['observation']\n", "        )\n", "        \n", "        time.sleep(1)  # Rate limiting\n", "        \n", "        # Calculate location score (second most important)\n", "        print(\"  📍 Calculating location score...\")\n", "        location_score = calculate_location_score_with_reasoning(\n", "            gt_obs['location'], model_obs['location']\n", "        )\n", "        \n", "        time.sleep(1)  # Rate limiting\n", "        \n", "        # Calculate heuristics score\n", "        print(\"  🎯 Calculating heuristics score...\")\n", "        heuristics_score = calculate_heuristics_score_with_reasoning(\n", "            gt_obs['heuristics_violated'], model_obs['heuristics_violated']\n", "        )\n", "        \n", "        time.sleep(1)  # Rate limiting\n", "        \n", "        # Calculate severity score\n", "        print(\"  ⚡ Calculating severity score...\")\n", "        severity_score = calculate_severity_score_with_reasoning(\n", "            gt_obs['severity'], model_obs['severity']\n", "        )\n", "        \n", "        time.sleep(1)  # Rate limiting\n", "        \n", "        # Compile results\n", "        result = {\n", "            'gt_id': gt_obs['id'],\n", "            'model_id': model_obs['id'],\n", "            'gt_observation_text': gt_obs['observation'],\n", "            'model_observation_text': model_obs['observation'],\n", "            'gt_location': gt_obs['location'],\n", "            'model_location': model_obs['location'],\n", "            'gt_severity': gt_obs['severity'],\n", "            'model_severity': model_obs['severity'],\n", "            'gt_heuristics': gt_obs['heuristics_violated'],\n", "            'model_heuristics': model_obs['heuristics_violated'],\n", "            \n", "            # Key points\n", "            'gt_key_points': gt_key_points.get('key_points', []),\n", "            'model_key_points': model_key_points.get('key_points', []),\n", "            \n", "            # Scores and reasoning\n", "            'observation_score': observation_score.get('observation_score', 0),\n", "            'observation_reasoning': observation_score.get('detailed_reasoning', ''),\n", "            'location_score': location_score.get('location_score', 0),\n", "            'location_reasoning': location_score.get('detailed_reasoning', ''),\n", "            'heuristics_score': heuristics_score.get('heuristics_score', 0),\n", "            'heuristics_reasoning': heuristics_score.get('detailed_reasoning', ''),\n", "            'severity_score': severity_score.get('severity_score', 0),\n", "            'severity_reasoning': severity_score.get('detailed_reasoning', ''),\n", "            \n", "            # Coverage info\n", "            'coverage_status': 'Covered',\n", "            'coverage_score': match['combined_score']\n", "        }\n", "        \n", "        evaluation_results.append(result)\n", "        \n", "        print(f\"  ✅ Evaluation complete - Obs: {result['observation_score']}, Loc: {result['location_score']}, Heur: {result['heuristics_score']}, Sev: {result['severity_score']}\")\n", "    \n", "    print(f\"\\n🎯 Detailed evaluation complete for {len(evaluation_results)} observations\")\n", "    return evaluation_results"]}, {"cell_type": "markdown", "metadata": {"id": "csv_generation_header"}, "source": ["## CSV Generation and Results\n", "\n", "Generate comprehensive CSV output with all scores and detailed reasoning."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "csv_generation_function"}, "outputs": [], "source": ["def generate_comprehensive_csv(evaluation_results: List[Dict], gt_observations: List[Dict]) -> pd.DataFrame:\n", "    \"\"\"Generate comprehensive CSV with all evaluation results\"\"\"\n", "    print(\"\\n📊 Generating comprehensive CSV report...\")\n", "    \n", "    # Create list for all GT observations (covered and uncovered)\n", "    all_results = []\n", "    covered_gt_ids = {result['gt_id'] for result in evaluation_results}\n", "    \n", "    # Add covered observations\n", "    for result in evaluation_results:\n", "        csv_row = {\n", "            'GT_ID': result['gt_id'],\n", "            'Model_ID': result['model_id'],\n", "            'GT_Observation_Text': result['gt_observation_text'],\n", "            'Model_Observation_Text': result['model_observation_text'],\n", "            'GT_Location': result['gt_location'],\n", "            'Model_Location': result['model_location'],\n", "            'GT_Severity': result['gt_severity'],\n", "            'Model_Severity': result['model_severity'],\n", "            'GT_Heuristics': ', '.join(result['gt_heuristics']),\n", "            'Model_Heuristics': ', '.join(result['model_heuristics']),\n", "            'GT_Key_Points': ', '.join(result['gt_key_points']),\n", "            'Model_Key_Points': ', '.join(result['model_key_points']),\n", "            'Observation_Score': result['observation_score'],\n", "            'Observation_Reasoning': result['observation_reasoning'],\n", "            'Location_Score': result['location_score'],\n", "            'Location_Reasoning': result['location_reasoning'],\n", "            'Heuristics_Score': result['heuristics_score'],\n", "            'Heuristics_Reasoning': result['heuristics_reasoning'],\n", "            'Severity_Score': result['severity_score'],\n", "            'Severity_Reasoning': result['severity_reasoning'],\n", "            'Coverage_Status': result['coverage_status'],\n", "            'Coverage_Score': result['coverage_score']\n", "        }\n", "        all_results.append(csv_row)\n", "    \n", "    # Add uncovered GT observations\n", "    for gt_obs in gt_observations:\n", "        if gt_obs['id'] not in covered_gt_ids:\n", "            csv_row = {\n", "                'GT_ID': gt_obs['id'],\n", "                'Model_ID': 'N/A',\n", "                'GT_Observation_Text': gt_obs['observation'],\n", "                'Model_Observation_Text': 'N/A - Not Covered',\n", "                'GT_Location': gt_obs['location'],\n", "                'Model_Location': 'N/A',\n", "                'GT_Severity': gt_obs['severity'],\n", "                'Model_Severity': 'N/A',\n", "                'GT_Heuristics': ', '.join(gt_obs['heuristics_violated']),\n", "                'Model_Heuristics': 'N/A',\n", "                'GT_Key_Points': 'N/A',\n", "                'Model_Key_Points': 'N/A',\n", "                'Observation_Score': 0,\n", "                'Observation_Reasoning': 'Not covered by model response',\n", "                'Location_Score': 0,\n", "                'Location_Reasoning': 'Not covered by model response',\n", "                'Heuristics_Score': 0,\n", "                'Heuristics_Reasoning': 'Not covered by model response',\n", "                'Severity_Score': 0,\n", "                'Severity_Reasoning': 'Not covered by model response',\n", "                'Coverage_Status': 'Not Covered',\n", "                'Coverage_Score': 0\n", "            }\n", "            all_results.append(csv_row)\n", "    \n", "    # Create DataFrame\n", "    df = pd.DataFrame(all_results)\n", "    \n", "    # Sort by GT_ID\n", "    df = df.sort_values('GT_ID')\n", "    \n", "    print(f\"✅ CSV report generated with {len(df)} rows\")\n", "    return df"]}, {"cell_type": "markdown", "metadata": {"id": "execution_header"}, "source": ["## Execute Evaluation\n", "\n", "Run the complete evaluation pipeline and generate results."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "execute_evaluation"}, "outputs": [], "source": ["# Step 1: Find coverage matches\n", "print(\"🚀 Starting Enhanced UX Audit Evaluation...\")\n", "print(\"=\" * 60)\n", "\n", "coverage_matches = find_coverage_matches(gt_observations, model_observations)\n", "\n", "# Step 2: Evaluate covered observations in detail\n", "evaluation_results = evaluate_covered_observations(coverage_matches)\n", "\n", "# Step 3: Generate comprehensive CSV\n", "results_df = generate_comprehensive_csv(evaluation_results, gt_observations)\n", "\n", "# Step 4: Save CSV file\n", "csv_filename = 'enhanced_ux_audit_evaluation_results.csv'\n", "results_df.to_csv(csv_filename, index=False)\n", "print(f\"\\n💾 Results saved to: {csv_filename}\")\n", "\n", "print(\"\\n✅ Enhanced UX Audit Evaluation Complete!\")"]}, {"cell_type": "markdown", "metadata": {"id": "summary_analysis_header"}, "source": ["## Summary Analysis and Insights\n", "\n", "Generate summary statistics and insights from the evaluation results."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "summary_analysis"}, "outputs": [], "source": ["def generate_summary_analysis(results_df: pd.DataFrame, gt_observations: List[Dict]) -> None:\n", "    \"\"\"Generate comprehensive summary analysis\"\"\"\n", "    print(\"\\n📈 ENHANCED UX AUDIT EVALUATION SUMMARY\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Coverage Analysis\n", "    total_gt = len(gt_observations)\n", "    covered_obs = len(results_df[results_df['Coverage_Status'] == 'Covered'])\n", "    coverage_percentage = (covered_obs / total_gt) * 100\n", "    \n", "    print(f\"\\n🎯 COVERAGE ANALYSIS:\")\n", "    print(f\"   Total GT Observations: {total_gt}\")\n", "    print(f\"   Covered Observations: {covered_obs}\")\n", "    print(f\"   Coverage Percentage: {coverage_percentage:.1f}%\")\n", "    print(f\"   Uncovered Observations: {total_gt - covered_obs}\")\n", "    \n", "    # Score Analysis (only for covered observations)\n", "    covered_df = results_df[results_df['Coverage_Status'] == 'Covered']\n", "    \n", "    if len(covered_df) > 0:\n", "        print(f\"\\n📊 SCORE ANALYSIS (Covered Observations Only):\")\n", "        print(f\"   Average Observation Score: {covered_df['Observation_Score'].mean():.1f}/100\")\n", "        print(f\"   Average Location Score: {covered_df['Location_Score'].mean():.1f}/100\")\n", "        print(f\"   Average Heuristics Score: {covered_df['Heuristics_Score'].mean():.1f}/100\")\n", "        print(f\"   Average Severity Score: {covered_df['Severity_Score'].mean():.1f}/100\")\n", "        \n", "        # Score distribution\n", "        print(f\"\\n📈 SCORE DISTRIBUTION:\")\n", "        for score_type in ['Observation_Score', 'Location_Score', 'Heuristics_Score', 'Severity_Score']:\n", "            high_scores = len(covered_df[covered_df[score_type] >= 80])\n", "            medium_scores = len(covered_df[(covered_df[score_type] >= 50) & (covered_df[score_type] < 80)])\n", "            low_scores = len(covered_df[covered_df[score_type] < 50])\n", "            \n", "            print(f\"   {score_type.replace('_', ' ')}:\")\n", "            print(f\"     High (80-100): {high_scores} observations\")\n", "            print(f\"     Medium (50-79): {medium_scores} observations\")\n", "            print(f\"     Low (0-49): {low_scores} observations\")\n", "    \n", "    # Priority Analysis\n", "    print(f\"\\n⚡ PRIORITY INSIGHTS:\")\n", "    print(f\"   🥇 Observation Score (Most Important): Focus on semantic understanding and key point extraction\")\n", "    print(f\"   🥈 Location Score (Second Most Important): Focus on accurate UI area identification\")\n", "    print(f\"   🥉 Heuristics & Severity: Important for comprehensive audit quality\")\n", "    \n", "    # Recommendations\n", "    if coverage_percentage < 80:\n", "        print(f\"\\n💡 RECOMMENDATIONS:\")\n", "        print(f\"   ⚠️  Coverage below 80% - Model may be missing important UX issues\")\n", "        print(f\"   📋 Review uncovered observations for patterns\")\n", "        print(f\"   🔧 Consider model fine-tuning or prompt engineering\")\n", "    \n", "    if len(covered_df) > 0:\n", "        avg_obs_score = covered_df['Observation_Score'].mean()\n", "        avg_loc_score = covered_df['Location_Score'].mean()\n", "        \n", "        if avg_obs_score < 70:\n", "            print(f\"   🎯 Observation scores need improvement - Focus on semantic understanding\")\n", "        if avg_loc_score < 70:\n", "            print(f\"   📍 Location scores need improvement - Focus on UI element identification\")\n", "\n", "# Generate summary analysis\n", "generate_summary_analysis(results_df, gt_observations)"]}, {"cell_type": "markdown", "metadata": {"id": "visualization_header"}, "source": ["## Visualization and Data Display\n", "\n", "Display key results and create visualizations for better understanding."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "visualization_display"}, "outputs": [], "source": ["# Display sample results\n", "print(\"\\n📋 SAMPLE EVALUATION RESULTS:\")\n", "print(\"=\" * 60)\n", "\n", "# Show first few covered observations\n", "covered_results = results_df[results_df['Coverage_Status'] == 'Covered'].head(3)\n", "\n", "for idx, row in covered_results.iterrows():\n", "    print(f\"\\n🔍 GT Observation {row['GT_ID']}:\")\n", "    print(f\"   Location: {row['GT_Location']}\")\n", "    print(f\"   Observation: {row['GT_Observation_Text'][:100]}...\")\n", "    print(f\"   Scores: Obs={row['Observation_Score']}, Loc={row['Location_Score']}, Heur={row['Heuristics_Score']}, Sev={row['Severity_Score']}\")\n", "    print(f\"   Key Reasoning: {row['Observation_Reasoning'][:150]}...\")\n", "\n", "# Create visualizations\n", "if len(results_df[results_df['Coverage_Status'] == 'Covered']) > 0:\n", "    covered_df = results_df[results_df['Coverage_Status'] == 'Covered']\n", "    \n", "    # Score comparison chart\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    fig.suptitle('Enhanced UX Audit Evaluation Results', fontsize=16, fontweight='bold')\n", "    \n", "    # Coverage pie chart\n", "    coverage_data = results_df['Coverage_Status'].value_counts()\n", "    axes[0, 0].pie(coverage_data.values, labels=coverage_data.index, autopct='%1.1f%%', startangle=90)\n", "    axes[0, 0].set_title('Coverage Analysis')\n", "    \n", "    # Score distribution\n", "    score_columns = ['Observation_Score', 'Location_Score', 'Heuristics_Score', 'Severity_Score']\n", "    score_means = [covered_df[col].mean() for col in score_columns]\n", "    score_labels = ['Observation\\n(Most Important)', 'Location\\n(2nd Important)', 'Heuristics', 'Severity']\n", "    \n", "    bars = axes[0, 1].bar(score_labels, score_means, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])\n", "    axes[0, 1].set_title('Average Scores (Covered Observations)')\n", "    axes[0, 1].set_ylabel('Score (0-100)')\n", "    axes[0, 1].set_ylim(0, 100)\n", "    \n", "    # Add value labels on bars\n", "    for bar, score in zip(bars, score_means):\n", "        axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, \n", "                        f'{score:.1f}', ha='center', va='bottom')\n", "    \n", "    # Score correlation heatmap\n", "    correlation_matrix = covered_df[score_columns].corr()\n", "    im = axes[1, 0].imshow(correlation_matrix, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)\n", "    axes[1, 0].set_xticks(range(len(score_columns)))\n", "    axes[1, 0].set_yticks(range(len(score_columns)))\n", "    axes[1, 0].set_xticklabels([col.replace('_Score', '') for col in score_columns], rotation=45)\n", "    axes[1, 0].set_yticklabels([col.replace('_Score', '') for col in score_columns])\n", "    axes[1, 0].set_title('Score Correlation Matrix')\n", "    \n", "    # Add correlation values\n", "    for i in range(len(score_columns)):\n", "        for j in range(len(score_columns)):\n", "            axes[1, 0].text(j, i, f'{correlation_matrix.iloc[i, j]:.2f}', \n", "                           ha='center', va='center', color='white' if abs(correlation_matrix.iloc[i, j]) > 0.5 else 'black')\n", "    \n", "    # Score distribution histogram\n", "    axes[1, 1].hist([covered_df['Observation_Score'], covered_df['Location_Score']], \n", "                   bins=10, alpha=0.7, label=['Observation (Most Important)', 'Location (2nd Important)'], \n", "                   color=['#FF6B6B', '#4ECDC4'])\n", "    axes[1, 1].set_title('Score Distribution (Priority Scores)')\n", "    axes[1, 1].set_xlabel('Score Range')\n", "    axes[1, 1].set_ylabel('Frequency')\n", "    axes[1, 1].legend()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Display CSV structure\n", "print(f\"\\n📊 CSV OUTPUT STRUCTURE:\")\n", "print(f\"   Total Columns: {len(results_df.columns)}\")\n", "print(f\"   Total Rows: {len(results_df)}\")\n", "print(f\"   Key Columns: {', '.join(results_df.columns[:8])}...\")\n", "\n", "print(f\"\\n✅ Enhanced UX Audit Evaluation Complete!\")\n", "print(f\"📁 Results saved to: enhanced_ux_audit_evaluation_results.csv\")\n", "print(f\"🎯 Focus on Observation Score (most important) and Location Score (second most important)\")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}